using System;
using System.IO;
using System.Text;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Management;
using System.Net.NetworkInformation;
using System.Linq;

namespace ClientSoftware
{
    /// <summary>
    /// 客户端软件示例 - 展示正确的授权流程
    /// 客户只能输入授权码，无法生成授权码
    /// </summary>
    public class LicenseManager
    {
        // 导入验证库函数
        [DllImport("LicenseValidator.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int ValidateLicenseWithSoftwareId(string licenseCode, string hardwareId, string softwareId);

        [DllImport("LicenseValidator.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int GetRemainingDays(string licenseCode);

        [DllImport("LicenseValidator.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern IntPtr GetLastError();

        [DllImport("LicenseValidator.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern void Cleanup();

        // 返回码常量
        private const int LICENSE_VALID = 0;
        private const int LICENSE_INVALID = 1;
        private const int LICENSE_EXPIRED = 2;
        private const int LICENSE_HARDWARE_MISMATCH = 3;
        private const int LICENSE_TIME_TAMPERED = 4;
        private const int LICENSE_FORMAT_ERROR = 5;

        private static string licenseFilePath = "license.dat";
        
        // 软件标识 - 每个软件都应该有唯一的标识
        private static string softwareId = "MyClientSoftware_v1.0";

        /// <summary>
        /// 验证授权码
        /// </summary>
        /// <param name="licenseCode">授权码</param>
        /// <returns>验证结果</returns>
        public static bool IsLicenseValid(string licenseCode)
        {
            try
            {
                string hardwareId = GetLocalHardwareFingerprint();
                int result = ValidateLicenseWithSoftwareId(licenseCode, hardwareId, softwareId);
                
                switch (result)
                {
                    case LICENSE_VALID:
                        return true;
                    case LICENSE_EXPIRED:
                        Console.WriteLine("❌ 授权已过期，请联系软件提供商续费");
                        break;
                    case LICENSE_HARDWARE_MISMATCH:
                        Console.WriteLine("❌ 此授权码不适用于当前电脑");
                        break;
                    case LICENSE_TIME_TAMPERED:
                        Console.WriteLine("❌ 检测到系统时间异常");
                        break;
                    case LICENSE_FORMAT_ERROR:
                        Console.WriteLine("❌ 授权码格式错误");
                        break;
                    case LICENSE_INVALID:
                        Console.WriteLine("❌ 授权码无效或不适用于此软件");
                        break;
                    default:
                        Console.WriteLine("❌ 授权验证失败");
                        break;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 授权验证异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取剩余天数
        /// </summary>
        /// <param name="licenseCode">授权码</param>
        /// <returns>剩余天数，-1表示错误</returns>
        public static int GetRemainingDaysCount(string licenseCode)
        {
            try
            {
                return GetRemainingDays(licenseCode);
            }
            catch
            {
                return -1;
            }
        }

        /// <summary>
        /// 获取本机硬件指纹
        /// </summary>
        /// <returns>硬件指纹</returns>
        public static string GetLocalHardwareFingerprint()
        {
            try
            {
                string cpuId = GetCpuId();
                string motherboardId = GetMotherboardId();
                string macAddress = GetMacAddress();
                
                string combined = $"{cpuId}|{motherboardId}|{macAddress}";
                
                using (var sha256 = SHA256.Create())
                {
                    byte[] hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(combined));
                    return "HW_" + Convert.ToHexString(hash)[..16];
                }
            }
            catch
            {
                return "HW_FALLBACK_" + Environment.MachineName;
            }
        }

        private static string GetCpuId()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        return obj["ProcessorId"]?.ToString() ?? "CPU_UNKNOWN";
                    }
                }
            }
            catch { }
            return "CPU_UNKNOWN";
        }

        private static string GetMotherboardId()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        return obj["SerialNumber"]?.ToString() ?? "MB_UNKNOWN";
                    }
                }
            }
            catch { }
            return "MB_UNKNOWN";
        }

        private static string GetMacAddress()
        {
            try
            {
                var networkInterface = NetworkInterface.GetAllNetworkInterfaces()
                    .FirstOrDefault(nic => nic.OperationalStatus == OperationalStatus.Up && 
                                          nic.NetworkInterfaceType != NetworkInterfaceType.Loopback);
                
                return networkInterface?.GetPhysicalAddress().ToString() ?? "MAC_UNKNOWN";
            }
            catch
            {
                return "MAC_UNKNOWN";
            }
        }

        /// <summary>
        /// 保存授权码到本地
        /// </summary>
        /// <param name="licenseCode">授权码</param>
        public static void SaveLicense(string licenseCode)
        {
            try
            {
                // 简单加密存储
                byte[] data = Encoding.UTF8.GetBytes(licenseCode);
                byte[] encrypted = ProtectedData.Protect(data, null, DataProtectionScope.LocalMachine);
                File.WriteAllBytes(licenseFilePath, encrypted);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存授权码失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从本地加载授权码
        /// </summary>
        /// <returns>授权码，如果不存在返回null</returns>
        public static string? LoadLicense()
        {
            try
            {
                if (!File.Exists(licenseFilePath))
                    return null;

                byte[] encrypted = File.ReadAllBytes(licenseFilePath);
                byte[] data = ProtectedData.Unprotect(encrypted, null, DataProtectionScope.LocalMachine);
                return Encoding.UTF8.GetString(data);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载授权码失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public static void CleanupResources()
        {
            try
            {
                Cleanup();
            }
            catch { }
        }
    }

    /// <summary>
    /// 客户端软件主程序
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("╔══════════════════════════════════════╗");
            Console.WriteLine("║        我的客户端软件 v1.0           ║");
            Console.WriteLine("║      Client Software Example        ║");
            Console.WriteLine("╚══════════════════════════════════════╝");
            Console.WriteLine();

            try
            {
                // 显示软件信息和硬件指纹
                Console.WriteLine("📋 软件信息:");
                Console.WriteLine($"   软件名称: 我的客户端软件");
                Console.WriteLine($"   软件版本: v1.0");
                Console.WriteLine($"   软件标识: MyClientSoftware_v1.0");
                Console.WriteLine();

                string hardwareFingerprint = LicenseManager.GetLocalHardwareFingerprint();
                Console.WriteLine("🔧 硬件信息:");
                Console.WriteLine($"   硬件指纹: {hardwareFingerprint}");
                Console.WriteLine();
                Console.WriteLine("💡 提示: 如需获取授权码，请将上述硬件指纹发送给软件提供商");
                Console.WriteLine();

                // 尝试加载已保存的授权码
                string? savedLicense = LicenseManager.LoadLicense();
                if (savedLicense != null)
                {
                    Console.WriteLine("🔍 检查已保存的授权码...");
                    if (LicenseManager.IsLicenseValid(savedLicense))
                    {
                        int remainingDays = LicenseManager.GetRemainingDaysCount(savedLicense);
                        Console.WriteLine($"✅ 授权验证成功！剩余天数: {remainingDays}");
                        Console.WriteLine();
                        RunApplication();
                        return;
                    }
                    else
                    {
                        Console.WriteLine("⚠️  已保存的授权码无效或已过期");
                        Console.WriteLine();
                    }
                }

                // 请求输入授权码
                Console.WriteLine("🔑 授权验证:");
                Console.WriteLine("   请输入您从软件提供商处获得的授权码");
                Console.Write("   授权码: ");
                string? inputLicense = Console.ReadLine();

                if (string.IsNullOrWhiteSpace(inputLicense))
                {
                    Console.WriteLine();
                    Console.WriteLine("❌ 未输入授权码，软件无法运行");
                    Console.WriteLine("   请联系软件提供商获取授权码");
                    return;
                }

                // 验证授权码
                Console.WriteLine();
                Console.WriteLine("🔍 正在验证授权码...");
                if (LicenseManager.IsLicenseValid(inputLicense))
                {
                    int remainingDays = LicenseManager.GetRemainingDaysCount(inputLicense);
                    Console.WriteLine($"✅ 授权验证成功！剩余天数: {remainingDays}");
                    
                    // 保存授权码
                    LicenseManager.SaveLicense(inputLicense);
                    Console.WriteLine("💾 授权码已保存，下次启动将自动验证");
                    Console.WriteLine();
                    
                    RunApplication();
                }
                else
                {
                    Console.WriteLine();
                    Console.WriteLine("❌ 授权验证失败，软件无法运行");
                    Console.WriteLine();
                    Console.WriteLine("可能的原因:");
                    Console.WriteLine("• 授权码输入错误（请检查是否完整复制）");
                    Console.WriteLine("• 授权码不适用于当前电脑");
                    Console.WriteLine("• 授权码已过期");
                    Console.WriteLine("• 授权码不适用于此软件版本");
                    Console.WriteLine();
                    Console.WriteLine("解决方法:");
                    Console.WriteLine("• 请确认硬件指纹正确");
                    Console.WriteLine("• 联系软件提供商重新获取授权码");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 程序运行异常: {ex.Message}");
            }
            finally
            {
                LicenseManager.CleanupResources();
                Console.WriteLine();
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// 运行主要应用程序逻辑
        /// </summary>
        static void RunApplication()
        {
            Console.WriteLine("╔══════════════════════════════════════╗");
            Console.WriteLine("║           软件正在运行...            ║");
            Console.WriteLine("╚══════════════════════════════════════╝");
            Console.WriteLine();
            
            Console.WriteLine("🚀 软件功能演示:");
            
            // 模拟应用程序运行
            for (int i = 1; i <= 5; i++)
            {
                Console.WriteLine($"   [{i}/5] 执行功能模块 {i}...");
                System.Threading.Thread.Sleep(800);
            }
            
            Console.WriteLine();
            Console.WriteLine("✅ 所有功能运行完成！");
            Console.WriteLine();
            Console.WriteLine("💡 这里是您的软件主要功能区域");
            Console.WriteLine("   您可以在这里添加您的业务逻辑");
        }
    }
}
