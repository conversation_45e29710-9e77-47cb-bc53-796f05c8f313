# 授权系统完整技术文档

## 📋 目录
- [系统概述](#系统概述)
- [核心逻辑](#核心逻辑)
- [技术架构](#技术架构)
- [集成方法](#集成方法)
- [使用指南](#使用指南)
- [安全机制](#安全机制)
- [故障排除](#故障排除)
- [紧急恢复方案](#紧急恢复方案)
- [源码重建指南](#源码重建指南)

---

## 🎯 系统概述

### 功能描述
本授权系统是一个基于时间控制的软件授权解决方案，主要功能：
- 生成包含时间限制的授权码
- 验证授权码的有效性和时间限制
- 防止时间篡改和跨机器使用
- 支持跨编程语言集成

### 核心特性
- ✅ **时间控制**：基于日历天数的授权期限
- ✅ **防篡改**：多重时间验证，防止系统时间修改
- ✅ **硬件绑定**：防止授权码在不同机器间传播
- ✅ **跨语言**：支持C#、Python、Java等多种语言集成
- ✅ **安全加密**：RSA-2048位加密，确保授权码不可伪造

---

## 🔧 核心逻辑

### 授权码结构
```
授权码组成：
┌─────────────────────────────────────────────────────────┐
│ Base64编码的加密数据包                                    │
├─────────────────────────────────────────────────────────┤
│ 包含内容：                                              │
│ • 开始日期 (StartDate)                                  │
│ • 有效天数 (ValidDays)                                  │
│ • 硬件指纹 (HardwareFingerprint)                       │
│ • 数字签名 (RSA Signature)                             │
│ • 版本信息 (Version)                                   │
└─────────────────────────────────────────────────────────┘
```

### 验证流程
```mermaid
graph TD
    A[软件启动] --> B[读取授权码]
    B --> C[解密授权码]
    C --> D{授权码格式正确?}
    D -->|否| E[拒绝运行]
    D -->|是| F[验证硬件指纹]
    F --> G{硬件匹配?}
    G -->|否| E
    G -->|是| H[检查时间有效性]
    H --> I{在授权期内?}
    I -->|否| E
    I -->|是| J[时间篡改检测]
    J --> K{时间正常?}
    K -->|否| E
    K -->|是| L[允许运行]
```

### 时间验证策略
```
1. 基础验证：当前日期 <= 开始日期 + 有效天数
2. 篡改检测：
   - 时间锚点记录（上次运行时间）
   - 时间倒退检测（允许1-2小时，拒绝24小时以上）
   - 多重时间源交叉验证
   - 异常时间跳跃检测
```

---

## 🏗️ 技术架构

### 系统组件
```
LicenseSystem/
├── 🖥️ LicenseGenerator.exe        # 授权码生成器（WPF界面）
├── 📚 LicenseValidator.dll        # 核心验证库（C++）
├── ⚡ LicenseValidator.exe        # 命令行验证工具
├── 🔑 Keys/                       # RSA密钥对
│   ├── private.key               # 私钥（生成器使用）
│   └── public.key                # 公钥（验证器使用）
├── 📦 Bindings/                   # 各语言绑定
│   ├── CSharp/
│   ├── Python/
│   ├── Java/
│   └── JavaScript/
└── 📖 Examples/                   # 集成示例
    ├── CSharp-Example/
    ├── Python-Example/
    └── Java-Example/
```

### 技术栈
- **生成器**：C# + WPF（图形界面）
- **验证库**：C++（跨平台动态库）
- **加密算法**：RSA-2048 + AES-256
- **硬件指纹**：CPU ID + 主板序列号 + MAC地址

---

## 🔌 集成方法

### 方法1：动态库调用（推荐）

#### C# 集成
```csharp
using System.Runtime.InteropServices;

public class LicenseManager
{
    [DllImport("LicenseValidator.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern int ValidateLicense(string licenseCode, string hardwareId);
    
    [DllImport("LicenseValidator.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern IntPtr GetLastError();
    
    public static bool IsLicenseValid(string licenseCode)
    {
        string hardwareId = GetHardwareFingerprint();
        int result = ValidateLicense(licenseCode, hardwareId);
        return result == 0; // 0=成功, 1=无效, 2=过期, 3=硬件不匹配
    }
    
    private static string GetHardwareFingerprint()
    {
        // 获取硬件指纹的实现
        return "HW_" + Environment.MachineName + "_" + GetCpuId();
    }
}
```

#### Python 集成
```python
import ctypes
import platform

class LicenseManager:
    def __init__(self):
        if platform.system() == "Windows":
            self.lib = ctypes.CDLL('./LicenseValidator.dll')
        else:
            self.lib = ctypes.CDLL('./LicenseValidator.so')
        
        # 定义函数签名
        self.lib.ValidateLicense.argtypes = [ctypes.c_char_p, ctypes.c_char_p]
        self.lib.ValidateLicense.restype = ctypes.c_int
    
    def is_license_valid(self, license_code):
        hardware_id = self.get_hardware_fingerprint()
        result = self.lib.ValidateLicense(
            license_code.encode('utf-8'),
            hardware_id.encode('utf-8')
        )
        return result == 0
    
    def get_hardware_fingerprint(self):
        # 获取硬件指纹的实现
        return f"HW_{platform.node()}_{self.get_cpu_id()}"
```

#### Java 集成
```java
import com.sun.jna.Library;
import com.sun.jna.Native;

public class LicenseManager {
    public interface LicenseValidator extends Library {
        LicenseValidator INSTANCE = Native.load("LicenseValidator", LicenseValidator.class);
        
        int ValidateLicense(String licenseCode, String hardwareId);
        String GetLastError();
    }
    
    public static boolean isLicenseValid(String licenseCode) {
        String hardwareId = getHardwareFingerprint();
        int result = LicenseValidator.INSTANCE.ValidateLicense(licenseCode, hardwareId);
        return result == 0;
    }
    
    private static String getHardwareFingerprint() {
        // 获取硬件指纹的实现
        return "HW_" + System.getProperty("user.name") + "_" + getCpuId();
    }
}
```

### 方法2：命令行调用
```bash
# 任何语言都可以调用命令行
LicenseValidator.exe "授权码" "硬件指纹"
# 返回码：0=有效, 1=无效, 2=过期, 3=硬件不匹配
```

---

## 📖 使用指南

### 生成授权码
1. 运行 `LicenseGenerator.exe`
2. 输入客户硬件指纹
3. 设置授权天数
4. 点击"生成授权码"
5. 复制生成的授权码给客户

### 集成到软件中
1. 将 `LicenseValidator.dll` 复制到软件目录
2. 将 `public.key` 复制到软件目录
3. 在软件启动时调用验证函数
4. 根据返回结果决定是否允许运行

### 客户端使用
1. 客户首次运行软件时提示输入授权码
2. 软件验证授权码并保存（加密存储）
3. 后续运行时自动验证，无需重复输入

---

## 🔒 安全机制

### 加密保护
- **RSA-2048位**：用于授权码签名和验证
- **AES-256**：用于敏感数据加密存储
- **硬件绑定**：防止授权码跨机器使用

### 防篡改措施
- **时间锚点**：记录首次运行时间，检测异常时间跳跃
- **多重验证**：系统时间、文件时间戳、启动时间交叉验证
- **渐进式检测**：允许合理时间调整，严格检测恶意篡改

### 存储安全
- 授权信息加密存储在注册表/配置文件中
- 使用机器特征作为加密密钥的一部分
- 防止简单的文件复制攻击

---

## 🚨 故障排除

### 常见问题

#### 1. 授权码无效
**可能原因：**
- 授权码格式错误
- 硬件指纹不匹配
- 授权码已过期

**解决方法：**
- 检查授权码是否完整复制
- 重新获取硬件指纹
- 确认授权期限

#### 2. 时间验证失败
**可能原因：**
- 系统时间被大幅修改
- 时间锚点文件损坏
- 系统时钟异常

**解决方法：**
- 恢复正确的系统时间
- 删除时间锚点文件重新初始化
- 联系技术支持重新授权

#### 3. 硬件指纹变化
**可能原因：**
- 硬件更换
- 系统重装
- 虚拟机环境变化

**解决方法：**
- 重新生成授权码
- 使用备用硬件指纹算法
- 联系技术支持

---

## 🆘 紧急恢复方案

### 场景1：授权软件丢失
如果 `LicenseGenerator.exe` 丢失，可以通过以下方式恢复：

#### 方法1：从源码重建
1. 使用本文档的[源码重建指南](#源码重建指南)
2. 重新编译生成器
3. 使用备份的私钥文件

#### 方法2：手动生成授权码
```csharp
// 紧急授权码生成代码（需要私钥）
public static string GenerateEmergencyLicense(DateTime startDate, int validDays, string hardwareId)
{
    var licenseData = new
    {
        StartDate = startDate,
        ValidDays = validDays,
        HardwareId = hardwareId,
        Version = "1.0"
    };
    
    string json = JsonConvert.SerializeObject(licenseData);
    byte[] data = Encoding.UTF8.GetBytes(json);
    
    // 使用RSA私钥签名
    using (var rsa = RSA.Create())
    {
        rsa.ImportRSAPrivateKey(File.ReadAllBytes("private.key"), out _);
        byte[] signature = rsa.SignData(data, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
        
        var package = new { Data = data, Signature = signature };
        string packageJson = JsonConvert.SerializeObject(package);
        return Convert.ToBase64String(Encoding.UTF8.GetBytes(packageJson));
    }
}
```

### 场景2：密钥文件丢失
如果RSA密钥对丢失：

#### 临时解决方案
1. 生成新的密钥对
2. 重新编译验证库
3. 为所有客户重新生成授权码

#### 预防措施
- 定期备份密钥文件到安全位置
- 使用密钥托管服务
- 建立密钥恢复流程

### 场景3：验证库损坏
如果 `LicenseValidator.dll` 损坏：

#### 快速恢复
1. 从备份恢复DLL文件
2. 重新编译验证库
3. 使用命令行版本临时替代

### 场景4：客户端无法验证
如果客户端软件无法验证授权：

#### 诊断步骤
```bash
# 1. 检查硬件指纹
LicenseExample.exe --show-fingerprint

# 2. 测试授权码格式
echo "授权码" | base64 -d

# 3. 检查时间锚点
dir %TEMP%\license_anchor.dat

# 4. 验证DLL依赖
dumpbin /dependents LicenseValidator.dll
```

#### 解决方案
- 重新获取正确的硬件指纹
- 检查授权码是否完整
- 删除时间锚点文件重新初始化
- 安装必要的运行时库

---

## 🔧 源码重建指南

### 环境要求
- Visual Studio 2022 或更高版本
- .NET 6.0 或更高版本
- C++ 编译环境（MSVC）

### 重建步骤

#### 1. 创建解决方案结构
```
LicenseSystem.sln
├── LicenseGenerator/          # C# WPF 项目
├── LicenseValidator/          # C++ DLL 项目
├── LicenseValidatorCLI/       # C# 命令行项目
└── Common/                    # 共享代码
```

#### 2. 核心算法实现
```csharp
// RSA密钥生成
public static (string privateKey, string publicKey) GenerateKeyPair()
{
    using (var rsa = RSA.Create(2048))
    {
        var privateKey = Convert.ToBase64String(rsa.ExportRSAPrivateKey());
        var publicKey = Convert.ToBase64String(rsa.ExportRSAPublicKey());
        return (privateKey, publicKey);
    }
}

// 硬件指纹生成
public static string GetHardwareFingerprint()
{
    var cpuId = GetCpuId();
    var motherboardId = GetMotherboardId();
    var macAddress = GetMacAddress();
    
    var combined = $"{cpuId}|{motherboardId}|{macAddress}";
    using (var sha256 = SHA256.Create())
    {
        var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(combined));
        return Convert.ToBase64String(hash);
    }
}
```

#### 3. 编译配置
```xml
<!-- LicenseGenerator.csproj -->
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
</Project>
```

#### 4. 命令行授权码生成器（紧急备用）
```csharp
// EmergencyLicenseGenerator.cs - 控制台版本
using System;
using System.IO;
using System.Text;
using System.Security.Cryptography;
using Newtonsoft.Json;

class EmergencyLicenseGenerator
{
    static void Main(string[] args)
    {
        if (args.Length != 3)
        {
            Console.WriteLine("用法: EmergencyLicenseGenerator.exe <硬件指纹> <开始日期> <有效天数>");
            Console.WriteLine("示例: EmergencyLicenseGenerator.exe HW_ABC123 2024-01-01 30");
            return;
        }

        string hardwareId = args[0];
        DateTime startDate = DateTime.Parse(args[1]);
        int validDays = int.Parse(args[2]);

        string licenseCode = GenerateLicense(startDate, validDays, hardwareId);
        Console.WriteLine($"授权码: {licenseCode}");

        // 保存到文件
        string fileName = $"License_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
        File.WriteAllText(fileName, licenseCode);
        Console.WriteLine($"已保存到: {fileName}");
    }

    static string GenerateLicense(DateTime startDate, int validDays, string hardwareId)
    {
        string simpleFormat = $"{((DateTimeOffset)startDate).ToUnixTimeSeconds()}|{validDays}|{hardwareId}";
        byte[] data = Encoding.UTF8.GetBytes(simpleFormat);
        return Convert.ToBase64String(data);
    }
}
```

### 关键文件清单
- `LicenseGenerator.exe` - 授权码生成器（WPF图形界面）
- `EmergencyLicenseGenerator.exe` - 紧急授权码生成器（控制台）
- `LicenseValidator.dll` - 验证库
- `private.key` - RSA私钥（生成器使用）
- `public.key` - RSA公钥（验证器使用）
- 本文档 - 完整技术说明

---

## 📞 技术支持

### 联系信息
- 开发者：[您的姓名]
- 邮箱：[您的邮箱]
- 创建日期：2024年
- 版本：1.0

### 更新日志
- v1.0 (2024-01-01): 初始版本发布

---

**⚠️ 重要提醒：**
1. 请妥善保管RSA私钥文件，丢失将无法生成新的授权码
2. 定期备份本文档和相关文件
3. 建议建立版本控制系统管理源码
4. 在生产环境部署前充分测试所有功能

---

*本文档包含了授权系统的完整技术细节，请妥善保管并定期更新。*
