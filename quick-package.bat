@echo off
echo ========================================
echo 一键打包 - 生成独立exe文件
echo ========================================
echo.

:: 检查.NET SDK
where dotnet >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 .NET SDK
    echo 请先安装 .NET 6.0 SDK 或更高版本
    echo 下载地址: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

:: 创建输出目录
if exist "Standalone" rmdir /s /q "Standalone"
mkdir Standalone

echo 正在打包授权系统为独立exe文件...
echo.

echo [1/4] 打包授权码生成器...
cd LicenseGenerator
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true -o ..\Standalone\ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 授权码生成器打包失败
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✓ LicenseGenerator.exe (约50MB)

echo [2/4] 打包紧急生成器...
cd EmergencyLicenseGenerator
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true -o ..\Standalone\ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 紧急生成器打包失败
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✓ EmergencyLicenseGenerator.exe (约30MB)

echo [3/4] 打包示例程序...
cd Examples\CSharp-Example
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true -o ..\..\Standalone\ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 示例程序打包失败
    cd ..\..
    pause
    exit /b 1
)
cd ..\..
echo ✓ LicenseExample.exe (约30MB)

echo [4/4] 复制必要文件...
:: 复制验证库（如果存在）
if exist "bin\LicenseValidator.dll" (
    copy "bin\LicenseValidator.dll" "Standalone\" >nul
    echo ✓ LicenseValidator.dll
) else (
    echo ⚠ 警告: 未找到 LicenseValidator.dll，请先运行 build.bat
)

:: 复制文档
copy "*.md" "Standalone\" >nul 2>nul
echo ✓ 文档文件

:: 创建启动脚本
echo @echo off > "Standalone\启动授权码生成器.bat"
echo echo 启动授权码生成器... >> "Standalone\启动授权码生成器.bat"
echo LicenseGenerator.exe >> "Standalone\启动授权码生成器.bat"

echo @echo off > "Standalone\启动紧急生成器.bat"
echo echo 启动紧急生成器... >> "Standalone\启动紧急生成器.bat"
echo EmergencyLicenseGenerator.exe >> "Standalone\启动紧急生成器.bat"

echo @echo off > "Standalone\测试示例程序.bat"
echo echo 测试示例程序... >> "Standalone\测试示例程序.bat"
echo LicenseExample.exe >> "Standalone\测试示例程序.bat"

:: 创建使用说明
echo 授权系统 - 独立版本 > "Standalone\使用说明.txt"
echo ==================== >> "Standalone\使用说明.txt"
echo. >> "Standalone\使用说明.txt"
echo 本版本为独立exe文件，无需安装任何运行时环境 >> "Standalone\使用说明.txt"
echo 可以在任何Windows电脑上直接运行 >> "Standalone\使用说明.txt"
echo. >> "Standalone\使用说明.txt"
echo 快速开始: >> "Standalone\使用说明.txt"
echo 1. 双击"启动授权码生成器.bat"开始使用 >> "Standalone\使用说明.txt"
echo 2. 阅读 QUICK_START.md 了解详细使用方法 >> "Standalone\使用说明.txt"
echo 3. 参考 INTEGRATION_TUTORIAL.md 学习集成方法 >> "Standalone\使用说明.txt"

echo ✓ 启动脚本和说明文件

echo.
echo ========================================
echo 打包完成！
echo ========================================
echo.
echo 📁 生成的独立版本位于: Standalone\
echo.
echo 包含文件:
echo ├── LicenseGenerator.exe          (约50MB - 授权码生成器)
echo ├── EmergencyLicenseGenerator.exe (约30MB - 紧急生成器)
echo ├── LicenseExample.exe            (约30MB - 示例程序)
echo ├── LicenseValidator.dll          (验证库)
echo ├── 启动授权码生成器.bat           (快速启动)
echo ├── 启动紧急生成器.bat             (快速启动)
echo ├── 测试示例程序.bat               (快速启动)
echo ├── 使用说明.txt                  (使用指南)
echo └── 完整技术文档                   (*.md文件)
echo.
echo 🎯 特点:
echo ✅ 无需安装.NET运行时环境
echo ✅ 可在任何Windows电脑上直接运行
echo ✅ 单文件部署，便于分发
echo.
echo 🚀 下一步:
echo 1. 测试: 双击 Standalone\启动授权码生成器.bat
echo 2. 分发: 将 Standalone\ 文件夹打包发送给用户
echo 3. 使用: 用户解压后即可直接使用
echo.
pause
