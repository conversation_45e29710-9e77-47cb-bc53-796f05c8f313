# 授权系统 (License System)

一个完整的软件授权管理解决方案，支持时间控制、硬件绑定和防时间篡改。

## 🚀 快速开始

### 1. 构建系统
```bash
# Windows
build.bat

# Linux/macOS
chmod +x build.sh
./build.sh
```

### 2. 生成授权码
1. 运行 `bin/LicenseGenerator.exe`
2. 输入客户硬件指纹
3. 设置授权天数
4. 点击"生成授权码"

### 3. 集成到您的软件
参考 `Examples/` 目录中的示例代码。

## 📁 项目结构

```
LicenseSystem/
├── 📄 README.md                          # 本文件
├── 📄 LICENSE_SYSTEM_DOCUMENTATION.md    # 完整技术文档
├── 🔨 build.bat                          # Windows构建脚本
├── 🔨 build.sh                           # Linux/macOS构建脚本
├── 📁 LicenseGenerator/                  # 授权码生成器 (C# WPF)
├── 📁 LicenseValidator/                  # 验证库 (C++)
├── 📁 Examples/                          # 集成示例
│   ├── 📁 CSharp-Example/               # C# 集成示例
│   ├── 📁 Python-Example/               # Python 集成示例
│   └── 📁 Java-Example/                 # Java 集成示例
└── 📁 bin/                              # 构建输出目录
```

## ✨ 核心特性

- ✅ **时间控制**: 基于日历天数的授权期限
- ✅ **防篡改**: 多重时间验证，防止系统时间修改
- ✅ **硬件绑定**: 防止授权码在不同机器间传播
- ✅ **跨语言**: 支持 C#、Python、Java 等多种语言
- ✅ **安全加密**: RSA-2048位加密，确保授权码不可伪造

## 🔧 系统要求

### 开发环境
- **Windows**: Visual Studio 2022, .NET 6.0+, CMake 3.16+
- **Linux**: GCC 9+, .NET 6.0+, CMake 3.16+
- **macOS**: Xcode, .NET 6.0+, CMake 3.16+

### 运行环境
- **Windows**: .NET 6.0 Runtime
- **Linux**: .NET 6.0 Runtime, libstdc++
- **macOS**: .NET 6.0 Runtime

## 📖 使用指南

### 生成授权码

1. **获取客户硬件指纹**
   ```bash
   # 运行示例程序查看硬件指纹
   bin/LicenseExample.exe
   ```

2. **使用生成器创建授权码**
   - 运行 `LicenseGenerator.exe`
   - 输入硬件指纹
   - 设置授权天数
   - 生成并复制授权码

### 集成到 C# 项目

```csharp
// 1. 复制 LicenseValidator.dll 到项目输出目录
// 2. 添加验证代码

using System.Runtime.InteropServices;

[DllImport("LicenseValidator.dll")]
private static extern int ValidateLicense(string licenseCode, string hardwareId);

// 验证授权
string hardwareId = GetHardwareFingerprint();
int result = ValidateLicense(licenseCode, hardwareId);
if (result == 0) {
    // 授权有效，继续运行
} else {
    // 授权无效，退出程序
}
```

### 集成到 Python 项目

```python
import ctypes

# 加载验证库
lib = ctypes.CDLL('./LicenseValidator.dll')  # Windows
# lib = ctypes.CDLL('./LicenseValidator.so')   # Linux

# 验证授权
result = lib.ValidateLicense(
    license_code.encode('utf-8'),
    hardware_id.encode('utf-8')
)

if result == 0:
    print("授权有效")
else:
    print("授权无效")
```

## 🔒 安全机制

### 时间防篡改
- **时间锚点记录**: 记录首次运行时间
- **异常检测**: 检测大幅时间倒退（>24小时）
- **多重验证**: 系统时间、文件时间戳交叉验证

### 硬件绑定
- **CPU ID**: 处理器唯一标识
- **主板序列号**: 主板硬件标识
- **MAC地址**: 网络接口标识
- **组合哈希**: SHA256哈希确保唯一性

### 加密保护
- **RSA-2048**: 授权码数字签名
- **AES-256**: 本地数据加密存储
- **Base64编码**: 授权码传输格式

## 🚨 故障排除

### 常见问题

**Q: 授权码验证失败**
- 检查硬件指纹是否匹配
- 确认授权码是否完整复制
- 验证授权期限是否过期

**Q: 时间验证失败**
- 检查系统时间是否正确
- 删除时间锚点文件重新初始化
- 确认没有大幅修改系统时间

**Q: DLL加载失败**
- 确认 `LicenseValidator.dll` 在程序目录
- 检查 .NET Runtime 是否安装
- 验证操作系统架构匹配（x64）

### 紧急恢复

如果授权系统文件丢失，请参考 `LICENSE_SYSTEM_DOCUMENTATION.md` 中的详细恢复指南。

## 📞 技术支持

- 📧 邮箱: [您的邮箱]
- 📱 电话: [您的电话]
- 🌐 网站: [您的网站]

## 📄 许可证

本项目采用 [您的许可证] 许可证。

## 🔄 更新日志

### v1.0.0 (2024-01-01)
- ✨ 初始版本发布
- ✅ 基础授权验证功能
- ✅ 时间防篡改机制
- ✅ 硬件绑定功能
- ✅ 跨语言支持

---

**⚠️ 重要提醒**
1. 请妥善保管 RSA 私钥文件
2. 定期备份授权系统文件
3. 在生产环境部署前充分测试
4. 建议建立版本控制管理源码

**📚 详细文档**: 请阅读 `LICENSE_SYSTEM_DOCUMENTATION.md` 获取完整的技术文档和使用指南。
