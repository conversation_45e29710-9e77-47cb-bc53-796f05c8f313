@echo off
echo ========================================
echo 分离式授权系统打包脚本
echo ========================================
echo.
echo 此脚本将创建两个独立的包：
echo 1. 开发者工具包（您使用）
echo 2. 客户端集成包（集成到您的软件）
echo.

:: 检查.NET SDK
where dotnet >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 .NET SDK
    echo 请先安装 .NET 6.0 SDK 或更高版本
    pause
    exit /b 1
)

:: 检查CMake
where cmake >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 CMake
    echo 请先安装 CMake
    pause
    exit /b 1
)

:: 清理旧的打包目录
if exist "DeveloperTools" rmdir /s /q "DeveloperTools"
if exist "ClientIntegration" rmdir /s /q "ClientIntegration"

:: 创建目录结构
mkdir DeveloperTools
mkdir ClientIntegration

echo.
echo ========================================
echo 第一步：构建验证库
echo ========================================

:: 构建C++验证库
cd LicenseValidator
if not exist "build" mkdir build
cd build

cmake .. -G "Visual Studio 17 2022" -A x64 >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: CMake 配置失败
    cd ..\..
    pause
    exit /b 1
)

cmake --build . --config Release >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: C++ 库构建失败
    cd ..\..
    pause
    exit /b 1
)

:: 复制验证库到两个包
copy "lib\Release\LicenseValidator.dll" "..\..\DeveloperTools\" >nul
copy "lib\Release\LicenseValidator.dll" "..\..\ClientIntegration\" >nul
cd ..\..

echo ✓ 验证库构建完成

echo.
echo ========================================
echo 第二步：创建开发者工具包
echo ========================================

echo [1/3] 打包授权码生成器...
cd LicenseGenerator
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true -o ..\DeveloperTools\ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 授权码生成器打包失败
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✓ LicenseGenerator.exe

echo [2/3] 打包紧急生成器...
cd EmergencyLicenseGenerator
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true -o ..\DeveloperTools\ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 紧急生成器打包失败
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✓ EmergencyLicenseGenerator.exe

echo [3/3] 复制开发者文档和工具...
:: 复制完整文档
copy "*.md" "DeveloperTools\" >nul 2>nul
xcopy "Examples" "DeveloperTools\Examples\" /E /I /Q >nul 2>nul

:: 创建密钥文件（如果不存在）
if not exist "private.key" (
    echo 警告: 未找到私钥文件，将在首次运行生成器时创建
)
if exist "private.key" copy "private.key" "DeveloperTools\" >nul
if exist "public.key" copy "public.key" "DeveloperTools\" >nul

:: 创建开发者工具启动脚本
echo @echo off > "DeveloperTools\启动授权码生成器.bat"
echo echo ================================ >> "DeveloperTools\启动授权码生成器.bat"
echo echo 授权码生成器 - 开发者专用 >> "DeveloperTools\启动授权码生成器.bat"
echo echo ================================ >> "DeveloperTools\启动授权码生成器.bat"
echo echo. >> "DeveloperTools\启动授权码生成器.bat"
echo LicenseGenerator.exe >> "DeveloperTools\启动授权码生成器.bat"

echo @echo off > "DeveloperTools\启动紧急生成器.bat"
echo echo ================================ >> "DeveloperTools\启动紧急生成器.bat"
echo echo 紧急授权码生成器 - 命令行 >> "DeveloperTools\启动紧急生成器.bat"
echo echo ================================ >> "DeveloperTools\启动紧急生成器.bat"
echo echo. >> "DeveloperTools\启动紧急生成器.bat"
echo echo 用法: EmergencyLicenseGenerator.exe 软件标识 硬件指纹 开始日期 有效天数 >> "DeveloperTools\启动紧急生成器.bat"
echo echo 示例: EmergencyLicenseGenerator.exe MyApp_v1.0 HW_ABC123 2024-01-01 30 >> "DeveloperTools\启动紧急生成器.bat"
echo echo. >> "DeveloperTools\启动紧急生成器.bat"
echo pause >> "DeveloperTools\启动紧急生成器.bat"

echo ✓ 开发者工具包创建完成

echo.
echo ========================================
echo 第三步：创建客户端集成包
echo ========================================

echo [1/2] 创建客户端软件示例...
cd Examples\ClientSoftware-Example
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true -o ..\..\ClientIntegration\ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 客户端示例打包失败
    cd ..\..
    pause
    exit /b 1
)
cd ..\..
echo ✓ 客户端软件示例程序

echo [2/2] 创建集成文件和文档...
:: 复制公钥（客户端只需要公钥）
if exist "public.key" copy "public.key" "ClientIntegration\" >nul

:: 复制集成相关文档
copy "INTEGRATION_TUTORIAL.md" "ClientIntegration\" >nul
copy "QUICK_START.md" "ClientIntegration\" >nul

:: 复制集成示例代码
xcopy "Examples" "ClientIntegration\IntegrationExamples\" /E /I /Q >nul 2>nul

echo ✓ 客户端集成包创建完成

echo.
echo ========================================
echo 第四步：创建说明文档
echo ========================================

:: 创建开发者工具包说明
echo 授权系统 - 开发者工具包 > "DeveloperTools\使用说明.txt"
echo ========================== >> "DeveloperTools\使用说明.txt"
echo. >> "DeveloperTools\使用说明.txt"
echo 这是开发者专用的授权码生成工具包。 >> "DeveloperTools\使用说明.txt"
echo 客户无法使用此工具包生成授权码。 >> "DeveloperTools\使用说明.txt"
echo. >> "DeveloperTools\使用说明.txt"
echo 包含文件： >> "DeveloperTools\使用说明.txt"
echo - LicenseGenerator.exe: 图形界面授权码生成器 >> "DeveloperTools\使用说明.txt"
echo - EmergencyLicenseGenerator.exe: 命令行紧急生成器 >> "DeveloperTools\使用说明.txt"
echo - LicenseValidator.dll: 验证库（开发测试用） >> "DeveloperTools\使用说明.txt"
echo - private.key: 私钥文件（重要！请妥善保管） >> "DeveloperTools\使用说明.txt"
echo - public.key: 公钥文件 >> "DeveloperTools\使用说明.txt"
echo - 完整技术文档和示例代码 >> "DeveloperTools\使用说明.txt"
echo. >> "DeveloperTools\使用说明.txt"
echo 授权流程： >> "DeveloperTools\使用说明.txt"
echo 1. 客户发送硬件指纹给您 >> "DeveloperTools\使用说明.txt"
echo 2. 您使用生成器创建授权码 >> "DeveloperTools\使用说明.txt"
echo 3. 您发送授权码给客户 >> "DeveloperTools\使用说明.txt"
echo 4. 客户在软件中输入授权码激活 >> "DeveloperTools\使用说明.txt"
echo. >> "DeveloperTools\使用说明.txt"
echo 快速开始： >> "DeveloperTools\使用说明.txt"
echo 1. 双击"启动授权码生成器.bat" >> "DeveloperTools\使用说明.txt"
echo 2. 阅读完整文档了解详细使用方法 >> "DeveloperTools\使用说明.txt"

:: 创建客户端集成包说明
echo 授权系统 - 客户端集成包 > "ClientIntegration\集成说明.txt"
echo ======================== >> "ClientIntegration\集成说明.txt"
echo. >> "ClientIntegration\集成说明.txt"
echo 这是用于集成到您软件中的客户端组件。 >> "ClientIntegration\集成说明.txt"
echo 客户只能输入授权码，无法生成授权码。 >> "ClientIntegration\集成说明.txt"
echo. >> "ClientIntegration\集成说明.txt"
echo 包含文件： >> "ClientIntegration\集成说明.txt"
echo - LicenseValidator.dll: 验证库（集成到您的软件） >> "ClientIntegration\集成说明.txt"
echo - public.key: 公钥文件（验证用） >> "ClientIntegration\集成说明.txt"
echo - ClientSoftware.exe: 客户端软件示例程序 >> "ClientIntegration\集成说明.txt"
echo - IntegrationExamples/: 各语言集成示例代码 >> "ClientIntegration\集成说明.txt"
echo - 集成相关文档 >> "ClientIntegration\集成说明.txt"
echo. >> "ClientIntegration\集成说明.txt"
echo 集成步骤： >> "ClientIntegration\集成说明.txt"
echo 1. 将 LicenseValidator.dll 和 public.key 复制到您的软件目录 >> "ClientIntegration\集成说明.txt"
echo 2. 参考示例代码集成授权验证功能 >> "ClientIntegration\集成说明.txt"
echo 3. 在软件中添加授权码输入界面 >> "ClientIntegration\集成说明.txt"
echo 4. 测试授权验证流程 >> "ClientIntegration\集成说明.txt"
echo. >> "ClientIntegration\集成说明.txt"
echo 注意：此包不包含私钥，无法生成授权码！ >> "ClientIntegration\集成说明.txt"

echo ✓ 说明文档创建完成

echo.
echo ========================================
echo 打包完成！
echo ========================================
echo.
echo 📁 开发者工具包: DeveloperTools\
echo    ├── LicenseGenerator.exe          (约50MB - 图形界面生成器)
echo    ├── EmergencyLicenseGenerator.exe (约30MB - 命令行生成器)
echo    ├── LicenseValidator.dll          (验证库)
echo    ├── private.key                   (私钥 - 重要！)
echo    ├── public.key                    (公钥)
echo    ├── 启动脚本                       (快速启动)
echo    └── 完整文档和示例                 (技术资料)
echo.
echo 📁 客户端集成包: ClientIntegration\
echo    ├── LicenseValidator.dll          (验证库)
echo    ├── public.key                    (公钥)
echo    ├── ClientSoftware.exe            (客户端软件示例)
echo    ├── IntegrationExamples\          (各语言示例代码)
echo    └── 集成文档                       (集成指南)
echo.
echo 🎯 使用方式：
echo ✅ 您使用: DeveloperTools\ (生成授权码)
echo ✅ 客户使用: 您的软件 + ClientIntegration\组件 (输入授权码)
echo.
echo 🔒 安全特性：
echo ✅ 客户端不包含私钥，无法生成授权码
echo ✅ 只有您能控制授权码的生成
echo ✅ 客户只能输入授权码使用软件
echo.
pause
