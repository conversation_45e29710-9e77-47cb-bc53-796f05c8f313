# 分离式授权系统使用指南

## 🎯 新的授权模式

### 角色分离
- **您（开发者）**：拥有授权码生成器，完全控制授权
- **客户**：只能输入授权码，无法生成授权码

### 授权流程
```
客户 → 获取硬件指纹 → 发送给您
您 → 使用生成器 → 创建授权码 → 发送给客户
客户 → 输入授权码 → 软件激活 → 正常使用
```

## 📦 系统架构

### 开发者端（您保留）
```
DeveloperTools/
├── LicenseGenerator.exe          # 图形界面生成器
├── EmergencyLicenseGenerator.exe # 命令行生成器
├── LicenseValidator.dll          # 验证库
├── private.key                   # 私钥（重要！）
├── public.key                    # 公钥
└── 完整文档和示例
```

### 客户端（集成到您的软件）
```
YourSoftware/
├── YourApp.exe                   # 您的软件
├── LicenseValidator.dll          # 验证库
├── public.key                    # 公钥
└── 简单使用说明
```

## 🚀 快速开始

### 第一步：打包分离式系统
```bash
# 生成开发者工具包和客户端集成包
package-separated.bat
```

生成结果：
- **DeveloperTools/** - 您使用的工具包
- **ClientIntegration/** - 集成到您软件的组件

### 第二步：您的操作流程

#### 1. 接收客户请求
客户会发送给您：
```
硬件指纹: HW_A1B2C3D4E5F6G7H8
软件版本: MyApp_v1.0
授权期限: 30天
```

#### 2. 生成授权码
```bash
# 方法1：使用图形界面（推荐）
双击 DeveloperTools\启动授权码生成器.bat

# 方法2：使用命令行
EmergencyLicenseGenerator.exe MyApp_v1.0 HW_A1B2C3D4E5F6G7H8 2024-01-01 30
```

#### 3. 发送授权码给客户
```
您的软件授权码：
ABC123DEF456GHI789...

使用方法：
1. 运行软件
2. 在授权码输入框中粘贴此授权码
3. 点击确认即可激活软件

注意：此授权码仅适用于您的电脑，有效期30天。
```

### 第三步：客户的操作流程

#### 1. 获取硬件指纹
客户运行您的软件时会看到：
```
╔══════════════════════════════════════╗
║        我的客户端软件 v1.0           ║
╚══════════════════════════════════════╝

📋 软件信息:
   软件名称: 我的客户端软件
   软件版本: v1.0

🔧 硬件信息:
   硬件指纹: HW_A1B2C3D4E5F6G7H8

💡 提示: 如需获取授权码，请将上述硬件指纹发送给软件提供商

🔑 授权验证:
   请输入您从软件提供商处获得的授权码
   授权码: _
```

#### 2. 输入授权码
客户输入您发送的授权码后：
```
🔍 正在验证授权码...
✅ 授权验证成功！剩余天数: 30
💾 授权码已保存，下次启动将自动验证

╔══════════════════════════════════════╗
║           软件正在运行...            ║
╚══════════════════════════════════════╝
```

## 🔧 集成到您的软件

### 集成步骤

#### 1. 复制必要文件
```
从 ClientIntegration/ 复制到您的软件目录：
├── LicenseValidator.dll    # 验证库
├── public.key             # 公钥
└── 授权验证代码            # 参考示例代码
```

#### 2. 添加授权验证代码
```csharp
// 在您的软件启动时添加
static void Main(string[] args)
{
    Console.WriteLine("我的软件 v1.0");
    
    // 显示硬件指纹
    string hardwareId = LicenseManager.GetLocalHardwareFingerprint();
    Console.WriteLine($"硬件指纹: {hardwareId}");
    Console.WriteLine("如需授权，请将硬件指纹发送给软件提供商");
    
    // 检查授权
    string savedLicense = LicenseManager.LoadLicense();
    if (savedLicense != null && LicenseManager.IsLicenseValid(savedLicense))
    {
        Console.WriteLine("授权验证成功！");
        RunYourApplication(); // 运行您的软件
        return;
    }
    
    // 请求输入授权码
    Console.Write("请输入授权码: ");
    string licenseCode = Console.ReadLine();
    
    if (LicenseManager.IsLicenseValid(licenseCode))
    {
        LicenseManager.SaveLicense(licenseCode);
        Console.WriteLine("软件已激活！");
        RunYourApplication(); // 运行您的软件
    }
    else
    {
        Console.WriteLine("授权码无效，请联系软件提供商。");
    }
}
```

#### 3. 设置软件标识
```csharp
// 在 LicenseManager 中设置您的软件标识
private static string softwareId = "MyApp_v1.0"; // 改为您的软件标识
```

## 🔒 安全优势

### 相比原方案的优势
- ✅ **完全控制权** - 只有您能生成授权码
- ✅ **私钥安全** - 客户端不包含私钥
- ✅ **防止滥用** - 客户无法为他人生成授权码
- ✅ **商业价值** - 您控制每个授权的发放

### 安全机制
- 🔐 **私钥隔离** - 私钥只在您的开发环境
- 🛡️ **硬件绑定** - 授权码绑定特定硬件
- ⏰ **时间控制** - 您设置授权有效期
- 🔍 **软件绑定** - 授权码只能用于指定软件

## 💰 商业模式

### 授权管理
- 📊 **精确控制** - 您决定给谁授权，授权多长时间
- 💰 **灵活收费** - 可以按时间、功能、用户数收费
- 📈 **客户管理** - 维护客户授权记录和到期时间
- 🔄 **续费管理** - 到期后客户需要联系您续费

### 客户体验
- 🚀 **简单激活** - 客户只需输入授权码
- 📱 **快速响应** - 您可以快速为客户生成授权码
- 🔧 **无需安装** - 客户不需要安装额外工具
- 💬 **直接支持** - 有问题直接联系您

## 📞 客户支持

### 常见客户问题及回复

#### Q: 如何获取硬件指纹？
**A:** 运行软件，软件会自动显示硬件指纹，请将其发送给我们。

#### Q: 授权码在哪里输入？
**A:** 软件启动时会提示输入授权码，直接粘贴即可。

#### Q: 授权码无效怎么办？
**A:** 请检查：
- 授权码是否完整复制
- 硬件指纹是否正确
- 软件版本是否匹配
如仍有问题，请联系我们重新生成。

#### Q: 可以在其他电脑上使用吗？
**A:** 授权码绑定特定硬件，无法在其他电脑使用。如需在新电脑使用，请提供新电脑的硬件指纹。

### 您的支持流程
1. **收到客户硬件指纹**
2. **确认授权期限和软件版本**
3. **使用生成器创建授权码**
4. **发送授权码给客户**
5. **协助客户完成激活**

## 🎯 实际应用示例

### 示例1：桌面软件授权
```
客户: 我想购买您的图片编辑软件
您: 请先运行试用版获取硬件指纹
客户: 硬件指纹是 HW_ABC123...
您: 好的，30天授权码是 XYZ789...
客户: 输入后软件已激活，谢谢！
```

### 示例2：企业软件授权
```
企业: 我们需要10个用户的授权
您: 请提供10台电脑的硬件指纹
企业: 已发送硬件指纹列表
您: 已生成10个授权码，有效期1年
企业: 已分发给员工，都激活成功了
```

## 📋 检查清单

### 部署前检查
- [ ] 运行 `package-separated.bat` 成功
- [ ] DeveloperTools 包含完整工具
- [ ] ClientIntegration 不包含私钥
- [ ] 测试授权码生成和验证流程
- [ ] 确认客户端示例程序正常运行

### 分发前检查
- [ ] 您的软件已集成授权验证
- [ ] 客户端只包含必要文件
- [ ] 私钥文件安全保存
- [ ] 建立客户支持流程

## 🎉 总结

通过分离式授权系统，您获得了：

1. ✅ **完全控制** - 只有您能生成授权码
2. ✅ **安全可靠** - 客户端无法破解或滥用
3. ✅ **商业价值** - 支持灵活的商业模式
4. ✅ **客户体验** - 简单的激活流程

现在您可以安全地分发软件，同时保持对授权的完全控制！
