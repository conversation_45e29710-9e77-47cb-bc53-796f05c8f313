package com.example.license;

import java.util.Scanner;

/**
 * Java授权验证示例程序
 * 演示如何在Java应用中集成授权验证
 */
public class LicenseExample {
    
    public static void main(String[] args) {
        System.out.println("=== Java 授权验证示例程序 ===");
        System.out.println();
        
        LicenseManager licenseManager = new LicenseManager();
        Scanner scanner = new Scanner(System.in);
        
        try {
            // 显示硬件指纹
            String hardwareFingerprint = licenseManager.getHardwareFingerprint();
            System.out.println("本机硬件指纹: " + hardwareFingerprint);
            System.out.println();
            
            // 尝试加载已保存的授权码
            String savedLicense = licenseManager.loadLicense();
            if (savedLicense != null) {
                System.out.println("发现已保存的授权码，正在验证...");
                if (licenseManager.isLicenseValid(savedLicense)) {
                    int remainingDays = licenseManager.getRemainingDays(savedLicense);
                    System.out.println("✓ 授权验证成功！剩余天数: " + remainingDays);
                    runApplication();
                    return;
                } else {
                    System.out.println("✗ 已保存的授权码无效");
                }
            }
            
            // 请求输入授权码
            System.out.println("请输入授权码:");
            String inputLicense = scanner.nextLine().trim();
            
            if (inputLicense.isEmpty()) {
                System.out.println("未输入授权码，程序退出");
                return;
            }
            
            // 验证授权码
            System.out.println("正在验证授权码...");
            if (licenseManager.isLicenseValid(inputLicense)) {
                int remainingDays = licenseManager.getRemainingDays(inputLicense);
                System.out.println("✓ 授权验证成功！剩余天数: " + remainingDays);
                
                // 保存授权码
                licenseManager.saveLicense(inputLicense);
                System.out.println("授权码已保存，下次启动将自动验证");
                
                runApplication();
            } else {
                System.out.println("✗ 授权验证失败，程序无法运行");
                System.out.println("请联系软件提供商获取有效的授权码");
            }
            
        } catch (Exception e) {
            System.err.println("程序运行异常: " + e.getMessage());
            e.printStackTrace();
        } finally {
            licenseManager.cleanup();
            scanner.close();
            System.out.println("\n按回车键退出...");
            try {
                System.in.read();
            } catch (Exception e) {
                // 忽略异常
            }
        }
    }
    
    /**
     * 运行主要应用程序逻辑
     */
    private static void runApplication() {
        System.out.println();
        System.out.println("=== 应用程序正在运行 ===");
        System.out.println("这里是您的软件主要功能...");
        System.out.println();
        
        // 模拟应用程序运行
        for (int i = 1; i <= 5; i++) {
            System.out.println("执行任务 " + i + "/5...");
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        System.out.println("应用程序运行完成！");
    }
}
