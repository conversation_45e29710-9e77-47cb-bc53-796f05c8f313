Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LicenseGenerator", "LicenseGenerator\LicenseGenerator.csproj", "{1C994420-22D0-CECA-919E-FB6D3F52FC4A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1C994420-22D0-CECA-919E-FB6D3F52FC4A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1C994420-22D0-CECA-919E-FB6D3F52FC4A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1C994420-22D0-CECA-919E-FB6D3F52FC4A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1C994420-22D0-CECA-919E-FB6D3F52FC4A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F913CAEE-B54A-47B0-8465-2CA473829FAC}
	EndGlobalSection
EndGlobal
