# 授权系统部署和分发指南

## 📦 打包概述

授权系统提供两种打包方式，满足不同的部署需求：

### 🚀 独立版本（Standalone）- 推荐分发
- ✅ **无需安装运行时** - 可在任何Windows电脑上直接运行
- ✅ **完全自包含** - 包含所有必要的.NET运行时文件
- ✅ **单文件部署** - 每个程序都是独立的exe文件
- ⚠️ **文件较大** - 每个exe约30-50MB

### 💼 便携式版本（Portable）- 推荐开发
- ✅ **文件小巧** - 每个程序约1-5MB
- ✅ **启动快速** - 无需解压运行时
- ⚠️ **需要运行时** - 目标电脑需安装.NET 6.0 Runtime

## 🔧 打包步骤

### 第一步：运行打包脚本
```bash
# 自动打包所有组件
package.bat
```

### 第二步：选择合适的版本
```
package/
├── Portable/     # 便携式版本（需要.NET Runtime）
└── Standalone/   # 独立版本（无需安装运行时）
```

## 📁 打包内容

### 核心程序
| 文件名 | 便携式版本 | 独立版本 | 说明 |
|--------|------------|----------|------|
| LicenseGenerator.exe | ~5MB | ~50MB | 授权码生成器（图形界面） |
| EmergencyLicenseGenerator.exe | ~1MB | ~30MB | 紧急生成器（命令行） |
| LicenseValidator.dll | ~500KB | ~500KB | 验证库（集成用） |
| LicenseExample.exe | ~1MB | ~30MB | 集成示例程序 |

### 文档和示例
- 📖 **完整技术文档** - 所有MD文档文件
- 💻 **示例代码** - C#、Python、Java集成示例
- 🚀 **启动脚本** - 一键启动各个程序
- 📋 **使用说明** - 快速开始指南

## 🚀 部署方案

### 方案1：给客户分发（推荐独立版本）

#### 优势
- ✅ 客户无需安装任何运行时环境
- ✅ 兼容性最好，支持Windows 7及以上
- ✅ 部署简单，解压即用

#### 分发步骤
1. **打包**：运行 `package.bat`
2. **压缩**：将 `package/Standalone/` 打包为ZIP
3. **分发**：发送给客户
4. **使用**：客户解压后直接运行

#### 客户使用方法
```
1. 解压授权系统文件包
2. 双击"启动授权码生成器.bat"
3. 按照界面提示生成授权码
```

### 方案2：内部开发使用（推荐便携式版本）

#### 优势
- ✅ 文件小，传输快
- ✅ 启动速度快
- ✅ 适合频繁更新

#### 部署步骤
1. **安装运行时**：在开发机器上安装.NET 6.0 Runtime
2. **复制文件**：将 `package/Portable/` 复制到目标位置
3. **直接使用**：运行各个程序

## 🌐 分发策略

### 企业内部分发
```
推荐方案：便携式版本 + 统一安装.NET Runtime
优势：文件小，管理方便，更新快速
```

### 客户外部分发
```
推荐方案：独立版本
优势：无需客户安装任何环境，兼容性最好
```

### 在线分发
```
推荐方案：提供两个下载选项
- 标准版（独立版本）：适合大多数用户
- 精简版（便携式版本）：适合已安装.NET的用户
```

## 📋 系统要求

### 独立版本
- **操作系统**：Windows 7 SP1 及以上
- **架构**：x64（64位）
- **内存**：至少512MB可用内存
- **磁盘空间**：约200MB

### 便携式版本
- **操作系统**：Windows 7 SP1 及以上
- **架构**：x64（64位）
- **运行时**：.NET 6.0 Runtime 或更高版本
- **内存**：至少256MB可用内存
- **磁盘空间**：约50MB

## 🔒 安全考虑

### 文件完整性
- 建议对分发包进行数字签名
- 提供SHA256校验值验证文件完整性
- 使用可信的分发渠道

### 密钥管理
- ⚠️ **重要**：`private.key` 文件只能在生成端保存
- ✅ 客户端只需要 `public.key` 文件
- 🔐 建议将私钥文件加密存储

### 分发清单
```
客户端分发包应包含：
✅ LicenseGenerator.exe（生成器）
✅ EmergencyLicenseGenerator.exe（紧急生成器）
✅ LicenseValidator.dll（验证库）
✅ public.key（公钥文件）
✅ 完整文档和示例
❌ private.key（私钥文件 - 不要分发）
```

## 🚨 故障排除

### 问题1：独立版本无法运行
**可能原因：**
- 系统版本过低
- 缺少必要的系统组件

**解决方法：**
- 确认Windows版本为7 SP1及以上
- 安装最新的Visual C++ Redistributable

### 问题2：便携式版本提示缺少运行时
**解决方法：**
- 下载安装.NET 6.0 Runtime
- 或使用独立版本

### 问题3：程序被杀毒软件拦截
**解决方法：**
- 将程序添加到杀毒软件白名单
- 使用数字签名的版本

## 📞 技术支持

### 分发前检查清单
- [ ] 运行 `package.bat` 成功完成
- [ ] 测试独立版本在干净系统上运行
- [ ] 验证所有文档和示例完整
- [ ] 确认私钥文件未包含在客户端包中
- [ ] 测试授权码生成和验证流程

### 客户支持指南
1. **首次部署**：建议远程协助客户完成首次部署
2. **使用培训**：提供操作视频或文档
3. **技术支持**：建立技术支持渠道

## 📈 版本管理

### 版本号规则
```
主版本.次版本.修订版本.构建版本
例如：2.0.0.1
```

### 更新策略
- **主版本**：重大功能变更
- **次版本**：新功能添加
- **修订版本**：Bug修复
- **构建版本**：内部构建号

### 分发更新
1. **增量更新**：只分发变更的文件
2. **完整更新**：重新分发完整包
3. **自动更新**：考虑实现自动更新机制

---

## 🎉 总结

通过本指南，您可以：

1. ✅ **选择合适的打包方式** - 根据使用场景选择独立版本或便携式版本
2. ✅ **正确分发授权系统** - 确保客户能够顺利使用
3. ✅ **维护系统安全** - 保护密钥文件和系统完整性
4. ✅ **提供技术支持** - 解决常见部署问题

**推荐分发方案：**
- 🎯 **客户分发**：使用独立版本，无需安装运行时
- 🎯 **内部使用**：使用便携式版本，文件小巧高效

现在您可以将授权系统轻松分发到任何Windows电脑上使用！
