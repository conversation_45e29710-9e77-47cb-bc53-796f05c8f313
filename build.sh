#!/bin/bash

echo "========================================"
echo "授权系统构建脚本 (Linux/macOS)"
echo "========================================"
echo

# 检查必要工具
if ! command -v cmake &> /dev/null; then
    echo "错误: 未找到 CMake，请先安装 CMake"
    exit 1
fi

if ! command -v dotnet &> /dev/null; then
    echo "错误: 未找到 .NET SDK，请先安装 .NET 6.0 或更高版本"
    exit 1
fi

# 检查编译器
if ! command -v g++ &> /dev/null && ! command -v clang++ &> /dev/null; then
    echo "错误: 未找到 C++ 编译器，请安装 GCC 或 Clang"
    exit 1
fi

# 创建构建目录
mkdir -p build
mkdir -p bin

echo "1. 构建 C++ 验证库..."
cd LicenseValidator
mkdir -p build
cd build

# 配置 CMake
cmake .. -DCMAKE_BUILD_TYPE=Release
if [ $? -ne 0 ]; then
    echo "错误: CMake 配置失败"
    cd ../..
    exit 1
fi

# 构建项目
cmake --build . --config Release
if [ $? -ne 0 ]; then
    echo "错误: C++ 库构建失败"
    cd ../..
    exit 1
fi

# 复制生成的库文件
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    cp lib/libLicenseValidator.dylib ../../bin/LicenseValidator.dylib
else
    # Linux
    cp lib/libLicenseValidator.so ../../bin/LicenseValidator.so
fi

cd ../..
echo "✓ C++ 验证库构建完成"

echo
echo "2. 构建 C# 示例程序..."
cd Examples/CSharp-Example

dotnet build -c Release
if [ $? -ne 0 ]; then
    echo "错误: C# 示例构建失败"
    cd ../..
    exit 1
fi

# 复制示例程序
cp bin/Release/net6.0/LicenseExample ../../bin/
cp bin/Release/net6.0/LicenseExample.dll ../../bin/
cd ../..

echo "✓ 示例程序构建完成"

echo
echo "3. 复制必要文件..."
# 复制验证库到示例目录
if [[ "$OSTYPE" == "darwin"* ]]; then
    cp bin/LicenseValidator.dylib Examples/CSharp-Example/
    cp bin/LicenseValidator.dylib Examples/Python-Example/
else
    cp bin/LicenseValidator.so Examples/CSharp-Example/
    cp bin/LicenseValidator.so Examples/Python-Example/
fi

# 复制文档
cp LICENSE_SYSTEM_DOCUMENTATION.md bin/

echo "✓ 文件复制完成"

echo
echo "========================================"
echo "构建完成！"
echo "========================================"
echo
echo "生成的文件位于 bin/ 目录："
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "- LicenseValidator.dylib  (验证库 - macOS)"
else
    echo "- LicenseValidator.so     (验证库 - Linux)"
fi
echo "- LicenseExample          (C# 集成示例)"
echo "- LICENSE_SYSTEM_DOCUMENTATION.md (完整文档)"
echo
echo "集成示例位于 Examples/ 目录："
echo "- CSharp-Example/         (C# 集成示例)"
echo "- Python-Example/         (Python 集成示例)"
echo
echo "注意: 授权码生成器 (LicenseGenerator) 需要在 Windows 环境下构建"
echo "      因为它使用了 WPF 图形界面技术"
echo
echo "请阅读 LICENSE_SYSTEM_DOCUMENTATION.md 了解详细使用方法"
echo
