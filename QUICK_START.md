# 快速开始指南

## 🚀 5分钟快速上手

### 第一步：构建系统
```bash
# Windows
build.bat

# Linux/macOS  
chmod +x build.sh
./build.sh
```

### 第二步：测试系统
```bash
# Windows
test.bat
```

### 第三步：生成第一个授权码

1. **获取硬件指纹**
   ```bash
   cd bin
   LicenseExample.exe
   # 程序会显示本机硬件指纹，例如：HW_A1B2C3D4E5F6G7H8
   ```

2. **生成授权码**
   ```bash
   # 方法1：使用图形界面（推荐）
   LicenseGenerator.exe
   
   # 方法2：使用命令行
   EmergencyLicenseGenerator.exe HW_A1B2C3D4E5F6G7H8 2024-01-01 30
   ```

3. **测试授权码**
   - 运行 `LicenseExample.exe`
   - 输入生成的授权码
   - 验证是否成功

### 第四步：集成到您的项目

#### C# 项目集成
```csharp
// 1. 复制 LicenseValidator.dll 到项目输出目录
// 2. 添加以下代码

[DllImport("LicenseValidator.dll")]
private static extern int ValidateLicense(string licenseCode, string hardwareId);

// 使用示例
string hardwareId = GetHardwareFingerprint();
int result = ValidateLicense(licenseCode, hardwareId);
if (result == 0) {
    // 授权有效
    RunApplication();
} else {
    // 授权无效
    ShowLicenseError();
}
```

#### Python 项目集成
```python
import ctypes

# 加载验证库
lib = ctypes.CDLL('./LicenseValidator.dll')  # Windows
# lib = ctypes.CDLL('./LicenseValidator.so')   # Linux

# 验证授权
result = lib.ValidateLicense(
    license_code.encode('utf-8'),
    hardware_id.encode('utf-8')
)

if result == 0:
    print("授权有效")
    run_application()
else:
    print("授权无效")
```

#### Java 项目集成
```java
// 添加 JNA 依赖到 pom.xml
// 参考 Examples/Java-Example/pom.xml

import com.sun.jna.Library;
import com.sun.jna.Native;

public interface LicenseValidator extends Library {
    LicenseValidator INSTANCE = Native.load("LicenseValidator", LicenseValidator.class);
    int ValidateLicense(String licenseCode, String hardwareId);
}

// 使用示例
int result = LicenseValidator.INSTANCE.ValidateLicense(licenseCode, hardwareId);
if (result == 0) {
    // 授权有效
    runApplication();
}
```

## 📁 重要文件说明

```
bin/
├── LicenseGenerator.exe         # 主要授权码生成器（图形界面）
├── EmergencyLicenseGenerator.exe # 紧急生成器（命令行）
├── LicenseValidator.dll         # 验证库（核心组件）
├── LicenseExample.exe           # C# 集成示例
└── LicenseExample.jar           # Java 集成示例

Examples/
├── CSharp-Example/              # C# 完整集成示例
├── Python-Example/              # Python 完整集成示例
└── Java-Example/                # Java 完整集成示例
```

## 🔧 常见问题

**Q: 构建失败怎么办？**
- 确保安装了 .NET 6.0 SDK
- 确保安装了 CMake 和 C++ 编译器
- 检查防火墙是否阻止了下载

**Q: 授权码验证失败？**
- 检查硬件指纹是否正确
- 确认授权码完整复制
- 验证授权期限是否过期

**Q: DLL 加载失败？**
- 确保 `LicenseValidator.dll` 在程序目录
- 检查 .NET Runtime 是否安装
- 验证系统架构匹配（x64）

## 🛡️ 安全提醒

1. **妥善保管私钥文件** (`private.key`)
2. **定期备份授权系统文件**
3. **不要在公共场所生成授权码**
4. **建议使用版本控制管理源码**

## 📚 详细文档

- **完整技术文档**: `LICENSE_SYSTEM_DOCUMENTATION.md`
- **项目说明**: `README.md`
- **集成示例**: `Examples/` 目录

## 🆘 紧急情况

如果授权系统文件丢失：
1. 查看 `LICENSE_SYSTEM_DOCUMENTATION.md` 中的紧急恢复方案
2. 使用 `EmergencyLicenseGenerator.exe` 临时生成授权码
3. 从备份恢复完整系统

---

**🎉 恭喜！您已经成功设置了授权系统！**

现在您可以：
- 为客户生成授权码
- 将验证功能集成到任何软件中
- 享受安全可靠的软件授权管理
