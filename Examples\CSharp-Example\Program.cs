using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Management;
using System.Net.NetworkInformation;
using System.Linq;

namespace LicenseExample
{
    /// <summary>
    /// 授权管理器 - 演示如何在C#应用中集成授权验证
    /// </summary>
    public class LicenseManager
    {
        // 导入验证库函数
        [DllImport("LicenseValidator.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int ValidateLicenseWithSoftwareId(string licenseCode, string hardwareId, string softwareId);

        [DllImport("LicenseValidator.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int ValidateLicense(string licenseCode, string hardwareId);

        [DllImport("LicenseValidator.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int GetRemainingDays(string licenseCode);

        [DllImport("LicenseValidator.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern IntPtr GetLastError();

        [DllImport("LicenseValidator.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern IntPtr GetHardwareFingerprint();

        [DllImport("LicenseValidator.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int CheckTimeIntegrity();

        [DllImport("LicenseValidator.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern void Cleanup();

        // 返回码常量
        private const int LICENSE_VALID = 0;
        private const int LICENSE_INVALID = 1;
        private const int LICENSE_EXPIRED = 2;
        private const int LICENSE_HARDWARE_MISMATCH = 3;
        private const int LICENSE_TIME_TAMPERED = 4;
        private const int LICENSE_FORMAT_ERROR = 5;

        private static string licenseFilePath = "license.dat";

        // 软件标识 - 每个软件都应该有唯一的标识
        private static string softwareId = "CSharpExample_v1.0";

        /// <summary>
        /// 验证授权码（新版本 - 包含软件标识验证）
        /// </summary>
        /// <param name="licenseCode">授权码</param>
        /// <returns>验证结果</returns>
        public static bool IsLicenseValid(string licenseCode)
        {
            try
            {
                string hardwareId = GetLocalHardwareFingerprint();
                int result = ValidateLicenseWithSoftwareId(licenseCode, hardwareId, softwareId);

                switch (result)
                {
                    case LICENSE_VALID:
                        return true;
                    case LICENSE_EXPIRED:
                        Console.WriteLine("授权已过期");
                        break;
                    case LICENSE_HARDWARE_MISMATCH:
                        Console.WriteLine("硬件指纹不匹配");
                        break;
                    case LICENSE_TIME_TAMPERED:
                        Console.WriteLine("检测到时间篡改");
                        break;
                    case LICENSE_FORMAT_ERROR:
                        Console.WriteLine("授权码格式错误");
                        break;
                    case LICENSE_INVALID:
                        Console.WriteLine("授权码无效或不适用于此软件");
                        break;
                    default:
                        Console.WriteLine("授权验证失败");
                        break;
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"授权验证异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证授权码（兼容版本 - 不验证软件标识）
        /// </summary>
        /// <param name="licenseCode">授权码</param>
        /// <returns>验证结果</returns>
        public static bool IsLicenseValidLegacy(string licenseCode)
        {
            try
            {
                string hardwareId = GetLocalHardwareFingerprint();
                int result = ValidateLicense(licenseCode, hardwareId);
                return result == LICENSE_VALID;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"授权验证异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取剩余天数
        /// </summary>
        /// <param name="licenseCode">授权码</param>
        /// <returns>剩余天数，-1表示错误</returns>
        public static int GetRemainingDaysCount(string licenseCode)
        {
            try
            {
                return GetRemainingDays(licenseCode);
            }
            catch
            {
                return -1;
            }
        }

        /// <summary>
        /// 获取本机硬件指纹
        /// </summary>
        /// <returns>硬件指纹</returns>
        public static string GetLocalHardwareFingerprint()
        {
            try
            {
                // 首先尝试使用DLL中的函数
                IntPtr ptr = GetHardwareFingerprint();
                if (ptr != IntPtr.Zero)
                {
                    return Marshal.PtrToStringAnsi(ptr) ?? GenerateFallbackFingerprint();
                }
            }
            catch { }

            // 备用方案：自己生成
            return GenerateFallbackFingerprint();
        }

        private static string GenerateFallbackFingerprint()
        {
            try
            {
                string cpuId = GetCpuId();
                string motherboardId = GetMotherboardId();
                string macAddress = GetMacAddress();
                
                string combined = $"{cpuId}|{motherboardId}|{macAddress}";
                
                using (var sha256 = SHA256.Create())
                {
                    byte[] hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(combined));
                    return "HW_" + Convert.ToHexString(hash)[..16];
                }
            }
            catch
            {
                return "HW_FALLBACK_" + Environment.MachineName;
            }
        }

        private static string GetCpuId()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        return obj["ProcessorId"]?.ToString() ?? "CPU_UNKNOWN";
                    }
                }
            }
            catch { }
            return "CPU_UNKNOWN";
        }

        private static string GetMotherboardId()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        return obj["SerialNumber"]?.ToString() ?? "MB_UNKNOWN";
                    }
                }
            }
            catch { }
            return "MB_UNKNOWN";
        }

        private static string GetMacAddress()
        {
            try
            {
                var networkInterface = NetworkInterface.GetAllNetworkInterfaces()
                    .FirstOrDefault(nic => nic.OperationalStatus == OperationalStatus.Up && 
                                          nic.NetworkInterfaceType != NetworkInterfaceType.Loopback);
                
                return networkInterface?.GetPhysicalAddress().ToString() ?? "MAC_UNKNOWN";
            }
            catch
            {
                return "MAC_UNKNOWN";
            }
        }

        /// <summary>
        /// 保存授权码到本地
        /// </summary>
        /// <param name="licenseCode">授权码</param>
        public static void SaveLicense(string licenseCode)
        {
            try
            {
                // 简单加密存储（实际应用中应使用更强的加密）
                byte[] data = Encoding.UTF8.GetBytes(licenseCode);
                byte[] encrypted = ProtectedData.Protect(data, null, DataProtectionScope.LocalMachine);
                File.WriteAllBytes(licenseFilePath, encrypted);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存授权码失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从本地加载授权码
        /// </summary>
        /// <returns>授权码，如果不存在返回null</returns>
        public static string? LoadLicense()
        {
            try
            {
                if (!File.Exists(licenseFilePath))
                    return null;

                byte[] encrypted = File.ReadAllBytes(licenseFilePath);
                byte[] data = ProtectedData.Unprotect(encrypted, null, DataProtectionScope.LocalMachine);
                return Encoding.UTF8.GetString(data);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载授权码失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public static void CleanupResources()
        {
            try
            {
                Cleanup();
            }
            catch { }
        }
    }

    /// <summary>
    /// 示例应用程序主类
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== C# 授权验证示例程序 ===");
            Console.WriteLine();

            try
            {
                // 显示软件信息
                Console.WriteLine($"软件标识: {LicenseManager.softwareId}");

                // 显示硬件指纹
                string hardwareFingerprint = LicenseManager.GetLocalHardwareFingerprint();
                Console.WriteLine($"本机硬件指纹: {hardwareFingerprint}");
                Console.WriteLine();

                // 尝试加载已保存的授权码
                string? savedLicense = LicenseManager.LoadLicense();
                if (savedLicense != null)
                {
                    Console.WriteLine("发现已保存的授权码，正在验证...");
                    if (LicenseManager.IsLicenseValid(savedLicense))
                    {
                        int remainingDays = LicenseManager.GetRemainingDaysCount(savedLicense);
                        Console.WriteLine($"✓ 授权验证成功！剩余天数: {remainingDays}");
                        RunApplication();
                        return;
                    }
                    else
                    {
                        Console.WriteLine("✗ 已保存的授权码无效");
                    }
                }

                // 请求输入授权码
                Console.WriteLine("请输入授权码:");
                string? inputLicense = Console.ReadLine();

                if (string.IsNullOrWhiteSpace(inputLicense))
                {
                    Console.WriteLine("未输入授权码，程序退出");
                    return;
                }

                // 验证授权码
                Console.WriteLine("正在验证授权码...");
                if (LicenseManager.IsLicenseValid(inputLicense))
                {
                    int remainingDays = LicenseManager.GetRemainingDaysCount(inputLicense);
                    Console.WriteLine($"✓ 授权验证成功！剩余天数: {remainingDays}");
                    
                    // 保存授权码
                    LicenseManager.SaveLicense(inputLicense);
                    Console.WriteLine("授权码已保存，下次启动将自动验证");
                    
                    RunApplication();
                }
                else
                {
                    Console.WriteLine("✗ 授权验证失败，程序无法运行");
                    Console.WriteLine("请联系软件提供商获取有效的授权码");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"程序运行异常: {ex.Message}");
            }
            finally
            {
                LicenseManager.CleanupResources();
                Console.WriteLine("\n按任意键退出...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// 运行主要应用程序逻辑
        /// </summary>
        static void RunApplication()
        {
            Console.WriteLine();
            Console.WriteLine("=== 应用程序正在运行 ===");
            Console.WriteLine("这里是您的软件主要功能...");
            Console.WriteLine();
            
            // 模拟应用程序运行
            for (int i = 1; i <= 5; i++)
            {
                Console.WriteLine($"执行任务 {i}/5...");
                System.Threading.Thread.Sleep(1000);
            }
            
            Console.WriteLine("应用程序运行完成！");
        }
    }
}
