<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <AssemblyTitle>License Integration Example</AssemblyTitle>
    <AssemblyDescription>Example of how to integrate license validation</AssemblyDescription>
  </PropertyGroup>

  <ItemGroup>
    <None Update="LicenseValidator.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="public.key">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
