#include "LicenseValidator.h"
#include <string>
#include <fstream>
#include <chrono>
#include <ctime>
#include <vector>
#include <memory>
#include <sstream>
#include <iomanip>

#ifdef _WIN32
#include <windows.h>
#include <intrin.h>
#include <iphlpapi.h>
#pragma comment(lib, "iphlpapi.lib")
#else
#include <unistd.h>
#include <sys/utsname.h>
#include <ifaddrs.h>
#include <net/if.h>
#endif

// 全局变量
static std::string g_lastError;
static std::string g_hardwareFingerprint;

// Base64解码函数
std::vector<uint8_t> base64_decode(const std::string& encoded_string) {
    const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    std::vector<uint8_t> result;
    
    int in_len = encoded_string.size();
    int i = 0;
    int in = 0;
    uint8_t char_array_4[4], char_array_3[3];
    
    while (in_len-- && (encoded_string[in] != '=') && 
           (isalnum(encoded_string[in]) || (encoded_string[in] == '+') || (encoded_string[in] == '/'))) {
        char_array_4[i++] = encoded_string[in]; in++;
        if (i == 4) {
            for (i = 0; i < 4; i++)
                char_array_4[i] = chars.find(char_array_4[i]);
            
            char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
            char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
            char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];
            
            for (i = 0; (i < 3); i++)
                result.push_back(char_array_3[i]);
            i = 0;
        }
    }
    
    if (i) {
        for (int j = i; j < 4; j++)
            char_array_4[j] = 0;
        
        for (int j = 0; j < 4; j++)
            char_array_4[j] = chars.find(char_array_4[j]);
        
        char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
        char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
        char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];
        
        for (int j = 0; (j < i - 1); j++) result.push_back(char_array_3[j]);
    }
    
    return result;
}

// 获取CPU ID
std::string GetCpuId() {
#ifdef _WIN32
    int cpuInfo[4] = {0};
    __cpuid(cpuInfo, 1);
    
    std::stringstream ss;
    ss << std::hex << cpuInfo[0] << cpuInfo[1] << cpuInfo[2] << cpuInfo[3];
    return ss.str();
#else
    std::ifstream cpuinfo("/proc/cpuinfo");
    std::string line;
    while (std::getline(cpuinfo, line)) {
        if (line.find("processor") != std::string::npos) {
            return line.substr(line.find(":") + 2);
        }
    }
    return "unknown";
#endif
}

// 获取主板序列号
std::string GetMotherboardId() {
#ifdef _WIN32
    // 使用WMI获取主板序列号的简化版本
    return "MB_WIN32_" + std::to_string(GetTickCount64() % 1000000);
#else
    std::ifstream dmi("/sys/class/dmi/id/board_serial");
    std::string serial;
    if (dmi.is_open()) {
        std::getline(dmi, serial);
        return serial;
    }
    return "MB_LINUX_UNKNOWN";
#endif
}

// 获取MAC地址
std::string GetMacAddress() {
#ifdef _WIN32
    IP_ADAPTER_INFO adapterInfo[16];
    DWORD dwBufLen = sizeof(adapterInfo);
    
    DWORD dwStatus = GetAdaptersInfo(adapterInfo, &dwBufLen);
    if (dwStatus == ERROR_SUCCESS) {
        PIP_ADAPTER_INFO pAdapterInfo = adapterInfo;
        std::stringstream ss;
        for (int i = 0; i < 6; i++) {
            ss << std::hex << std::setw(2) << std::setfill('0') 
               << (int)pAdapterInfo->Address[i];
            if (i < 5) ss << ":";
        }
        return ss.str();
    }
#else
    struct ifaddrs *ifap, *ifa;
    if (getifaddrs(&ifap) == 0) {
        for (ifa = ifap; ifa != nullptr; ifa = ifa->ifa_next) {
            if (ifa->ifa_addr && ifa->ifa_addr->sa_family == AF_PACKET) {
                // 简化的MAC地址获取
                return "MAC_LINUX_" + std::string(ifa->ifa_name);
            }
        }
        freeifaddrs(ifap);
    }
#endif
    return "MAC_UNKNOWN";
}

// 生成硬件指纹
std::string GenerateHardwareFingerprint() {
    if (!g_hardwareFingerprint.empty()) {
        return g_hardwareFingerprint;
    }
    
    std::string cpuId = GetCpuId();
    std::string mbId = GetMotherboardId();
    std::string macAddr = GetMacAddress();
    
    // 简单的哈希组合
    std::string combined = cpuId + "|" + mbId + "|" + macAddr;
    
    // 简化的哈希算法（生产环境应使用SHA256）
    std::hash<std::string> hasher;
    size_t hashValue = hasher(combined);
    
    std::stringstream ss;
    ss << "HW_" << std::hex << hashValue;
    
    g_hardwareFingerprint = ss.str();
    return g_hardwareFingerprint;
}

// 获取当前时间戳
int64_t GetCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::seconds>(duration).count();
}

// 时间锚点文件路径
std::string GetTimeAnchorPath() {
#ifdef _WIN32
    char path[MAX_PATH];
    GetTempPathA(MAX_PATH, path);
    return std::string(path) + "license_anchor.dat";
#else
    return "/tmp/license_anchor.dat";
#endif
}

// 检查时间完整性
int CheckTimeIntegrityImpl() {
    std::string anchorPath = GetTimeAnchorPath();
    std::ifstream file(anchorPath, std::ios::binary);
    
    if (!file.is_open()) {
        // 首次运行，创建时间锚点
        return InitializeTimeAnchor();
    }
    
    int64_t lastTimestamp;
    file.read(reinterpret_cast<char*>(&lastTimestamp), sizeof(lastTimestamp));
    file.close();
    
    int64_t currentTimestamp = GetCurrentTimestamp();
    
    // 检查时间是否大幅倒退（超过24小时）
    if (currentTimestamp < lastTimestamp - 86400) {
        g_lastError = "Time has been tampered with (significant backward jump detected)";
        return LICENSE_TIME_TAMPERED;
    }
    
    // 更新时间锚点
    std::ofstream outFile(anchorPath, std::ios::binary);
    if (outFile.is_open()) {
        outFile.write(reinterpret_cast<const char*>(&currentTimestamp), sizeof(currentTimestamp));
        outFile.close();
    }
    
    return LICENSE_VALID;
}

// 解析授权码（更新版本）
struct LicenseInfo {
    int64_t startDate;
    int validDays;
    std::string hardwareId;
    std::string softwareId;  // 新增：软件标识
    std::string version;     // 新增：版本信息
    bool isValid;
};

LicenseInfo ParseLicense(const std::string& licenseCode) {
    LicenseInfo info = {0, 0, "", "", "", false};

    try {
        // 支持两种格式：
        // 新格式：START_TIMESTAMP|VALID_DAYS|HARDWARE_ID|SOFTWARE_ID|VERSION
        // 旧格式：START_TIMESTAMP|VALID_DAYS|HARDWARE_ID

        auto decoded = base64_decode(licenseCode);
        std::string decodedStr(decoded.begin(), decoded.end());

        size_t pos1 = decodedStr.find('|');
        size_t pos2 = decodedStr.find('|', pos1 + 1);
        size_t pos3 = decodedStr.find('|', pos2 + 1);
        size_t pos4 = decodedStr.find('|', pos3 + 1);

        if (pos1 != std::string::npos && pos2 != std::string::npos) {
            info.startDate = std::stoll(decodedStr.substr(0, pos1));
            info.validDays = std::stoi(decodedStr.substr(pos1 + 1, pos2 - pos1 - 1));

            if (pos3 != std::string::npos && pos4 != std::string::npos) {
                // 新格式：包含软件标识
                info.hardwareId = decodedStr.substr(pos2 + 1, pos3 - pos2 - 1);
                info.softwareId = decodedStr.substr(pos3 + 1, pos4 - pos3 - 1);
                info.version = decodedStr.substr(pos4 + 1);
            } else {
                // 旧格式：兼容性支持
                info.hardwareId = decodedStr.substr(pos2 + 1);
                info.softwareId = ""; // 空表示不验证软件标识
                info.version = "1.0";
            }
            info.isValid = true;
        }
    } catch (...) {
        g_lastError = "Failed to parse license code";
    }

    return info;
}

// 导出函数实现
extern "C" {

LICENSEVALIDATOR_API int ValidateLicenseWithSoftwareId(const char* licenseCode, const char* hardwareId, const char* softwareId) {
    if (!licenseCode || !hardwareId || !softwareId) {
        g_lastError = "Invalid parameters";
        return LICENSE_INVALID;
    }

    // 解析授权码
    LicenseInfo license = ParseLicense(std::string(licenseCode));
    if (!license.isValid) {
        g_lastError = "Invalid license format";
        return LICENSE_FORMAT_ERROR;
    }

    // 验证软件标识（如果授权码包含软件标识）
    if (!license.softwareId.empty() && license.softwareId != std::string(softwareId)) {
        g_lastError = "Software ID mismatch - this license is not for this software";
        return LICENSE_INVALID;
    }

    // 验证硬件指纹
    if (license.hardwareId != std::string(hardwareId)) {
        g_lastError = "Hardware fingerprint mismatch";
        return LICENSE_HARDWARE_MISMATCH;
    }

    // 检查时间完整性
    int timeCheck = CheckTimeIntegrityImpl();
    if (timeCheck != LICENSE_VALID) {
        return timeCheck;
    }

    // 检查授权期限
    int64_t currentTimestamp = GetCurrentTimestamp();
    int64_t expiryTimestamp = license.startDate + (license.validDays * 86400);

    if (currentTimestamp > expiryTimestamp) {
        g_lastError = "License has expired";
        return LICENSE_EXPIRED;
    }

    g_lastError = "License is valid";
    return LICENSE_VALID;
}

LICENSEVALIDATOR_API int ValidateLicense(const char* licenseCode, const char* hardwareId) {
    // 兼容性函数：调用新函数但不验证软件标识
    return ValidateLicenseWithSoftwareId(licenseCode, hardwareId, "");
}

LICENSEVALIDATOR_API int GetRemainingDays(const char* licenseCode) {
    if (!licenseCode) {
        return -1;
    }
    
    LicenseInfo license = ParseLicense(std::string(licenseCode));
    if (!license.isValid) {
        return -1;
    }
    
    int64_t currentTimestamp = GetCurrentTimestamp();
    int64_t expiryTimestamp = license.startDate + (license.validDays * 86400);
    
    if (currentTimestamp > expiryTimestamp) {
        return 0;
    }
    
    return static_cast<int>((expiryTimestamp - currentTimestamp) / 86400);
}

LICENSEVALIDATOR_API const char* GetLastError() {
    return g_lastError.c_str();
}

LICENSEVALIDATOR_API const char* GetHardwareFingerprint() {
    std::string fingerprint = GenerateHardwareFingerprint();
    return fingerprint.c_str();
}

LICENSEVALIDATOR_API int CheckTimeIntegrity() {
    return CheckTimeIntegrityImpl();
}

LICENSEVALIDATOR_API int InitializeTimeAnchor() {
    std::string anchorPath = GetTimeAnchorPath();
    int64_t currentTimestamp = GetCurrentTimestamp();
    
    std::ofstream file(anchorPath, std::ios::binary);
    if (!file.is_open()) {
        g_lastError = "Failed to create time anchor file";
        return LICENSE_INVALID;
    }
    
    file.write(reinterpret_cast<const char*>(&currentTimestamp), sizeof(currentTimestamp));
    file.close();
    
    return LICENSE_VALID;
}

LICENSEVALIDATOR_API void Cleanup() {
    g_lastError.clear();
    g_hardwareFingerprint.clear();
}

} // extern "C"
