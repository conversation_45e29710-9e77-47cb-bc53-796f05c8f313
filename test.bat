@echo off
echo ========================================
echo 授权系统测试脚本
echo ========================================
echo.

:: 检查必要文件
if not exist "bin\LicenseValidator.dll" (
    echo 错误: 未找到 LicenseValidator.dll
    echo 请先运行 build.bat 构建系统
    pause
    exit /b 1
)

if not exist "bin\LicenseExample.exe" (
    echo 错误: 未找到 LicenseExample.exe
    echo 请先运行 build.bat 构建系统
    pause
    exit /b 1
)

echo 1. 测试硬件指纹获取...
cd bin
LicenseExample.exe --show-fingerprint 2>nul
if %errorlevel% neq 0 (
    echo 警告: 无法通过参数获取硬件指纹，将通过交互方式测试
)

echo.
echo 2. 测试授权码生成器...
if exist "LicenseGenerator.exe" (
    echo ✓ 找到图形界面生成器: LicenseGenerator.exe
) else (
    echo ✗ 未找到图形界面生成器
)

if exist "EmergencyLicenseGenerator.exe" (
    echo ✓ 找到紧急生成器: EmergencyLicenseGenerator.exe
    echo 测试紧急生成器...
    EmergencyLicenseGenerator.exe TestApp_v1.0 HW_TEST123 2024-01-01 30 >nul 2>nul
    if %errorlevel% equ 0 (
        echo ✓ 紧急生成器工作正常
    ) else (
        echo ✗ 紧急生成器测试失败
    )
) else (
    echo ✗ 未找到紧急生成器
)

echo.
echo 3. 测试验证库...
echo 正在测试 C# 示例程序...
echo.
echo "=== 以下是 C# 示例程序的输出 ==="
LicenseExample.exe
echo "=== C# 示例程序测试完成 ==="

echo.
echo 4. 测试 Python 示例...
cd ..\Examples\Python-Example
if exist "license_manager.py" (
    python --version >nul 2>nul
    if %errorlevel% equ 0 (
        echo 正在测试 Python 示例程序...
        echo.
        echo "=== 以下是 Python 示例程序的输出 ==="
        python license_manager.py
        echo "=== Python 示例程序测试完成 ==="
    ) else (
        echo 警告: 未找到 Python，跳过 Python 示例测试
    )
) else (
    echo 错误: 未找到 Python 示例文件
)

echo.
echo 5. 测试 Java 示例...
cd ..\Java-Example
if exist "..\..\bin\LicenseExample.jar" (
    java -version >nul 2>nul
    if %errorlevel% equ 0 (
        echo 正在测试 Java 示例程序...
        echo.
        echo "=== 以下是 Java 示例程序的输出 ==="
        java -jar "..\..\bin\LicenseExample.jar"
        echo "=== Java 示例程序测试完成 ==="
    ) else (
        echo 警告: 未找到 Java，跳过 Java 示例测试
    )
) else (
    echo 警告: 未找到 Java 示例 JAR 文件，可能构建时跳过了
)

cd ..\..

echo.
echo 6. 检查文档完整性...
if exist "LICENSE_SYSTEM_DOCUMENTATION.md" (
    echo ✓ 找到完整技术文档
) else (
    echo ✗ 未找到技术文档
)

if exist "README.md" (
    echo ✓ 找到项目说明文档
) else (
    echo ✗ 未找到项目说明文档
)

echo.
echo 7. 检查密钥文件...
if exist "private.key" (
    echo ✓ 找到私钥文件
) else (
    echo ⚠ 未找到私钥文件（将在首次运行生成器时创建）
)

if exist "public.key" (
    echo ✓ 找到公钥文件
) else (
    echo ⚠ 未找到公钥文件（将在首次运行生成器时创建）
)

echo.
echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 测试结果总结:
echo - 验证库: 已测试
echo - C# 集成: 已测试
echo - Python 集成: 已测试（如果有Python环境）
echo - Java 集成: 已测试（如果有Java环境）
echo - 文档: 已检查
echo.
echo 如果所有测试都通过，说明授权系统构建成功！
echo.
echo 下一步:
echo 1. 运行 bin\LicenseGenerator.exe 生成授权码
echo 2. 阅读 LICENSE_SYSTEM_DOCUMENTATION.md 了解详细使用方法
echo 3. 参考 Examples\ 目录中的示例代码集成到您的项目
echo.
pause
