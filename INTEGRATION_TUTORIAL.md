# 独立授权集成教程

## 📋 概述

本教程详细说明如何为不同的软件设置独立授权，确保每个软件都需要专门的授权码才能运行。

## 🎯 独立授权的优势

- ✅ **精确控制** - 每个软件独立授权，可设置不同的有效期
- ✅ **灵活管理** - 可以单独控制某个软件的授权状态
- ✅ **安全隔离** - 一个软件的授权不会影响其他软件
- ✅ **商业价值** - 支持按软件收费的商业模式

## 🔧 核心概念

### 软件标识 (Software ID)
每个软件都需要一个唯一的标识符，格式建议：
```
软件名称_版本号
例如：MyApp_v1.0, Calculator_v2.1, DataTool_v3.0
```

### 授权码格式
新版本授权码包含软件标识：
```
Base64(开始时间戳|有效天数|硬件指纹|软件标识|版本)
```

## 📚 集成步骤

### 第一步：确定软件标识

为您的软件选择一个唯一标识：

```csharp
// 示例软件标识
string softwareId = "MyCalculator_v1.0";  // 计算器软件
string softwareId = "DataAnalyzer_v2.1";  // 数据分析软件
string softwareId = "ImageEditor_v3.0";   // 图像编辑软件
```

**命名规范：**
- 使用英文字母、数字和下划线
- 包含软件名称和版本号
- 避免特殊字符和空格
- 保持简洁明了

### 第二步：生成授权码

#### 使用图形界面生成器
1. 运行 `LicenseGenerator.exe`
2. 输入**软件标识**（如：MyCalculator_v1.0）
3. 输入客户的**硬件指纹**
4. 设置**授权天数**
5. 点击"生成授权码"

#### 使用命令行生成器
```bash
EmergencyLicenseGenerator.exe MyCalculator_v1.0 HW_ABC123 2024-01-01 30
```

### 第三步：集成验证代码

## 🔌 各语言集成方法

### C# 集成

#### 1. 添加验证库引用
```csharp
using System.Runtime.InteropServices;

[DllImport("LicenseValidator.dll", CallingConvention = CallingConvention.Cdecl)]
private static extern int ValidateLicenseWithSoftwareId(string licenseCode, string hardwareId, string softwareId);
```

#### 2. 设置软件标识
```csharp
public class LicenseManager
{
    // 重要：每个软件都要设置不同的标识
    private static string softwareId = "MyCalculator_v1.0";
    
    public static bool IsLicenseValid(string licenseCode)
    {
        string hardwareId = GetHardwareFingerprint();
        int result = ValidateLicenseWithSoftwareId(licenseCode, hardwareId, softwareId);
        return result == 0; // 0 = 授权有效
    }
}
```

#### 3. 在程序启动时验证
```csharp
static void Main(string[] args)
{
    Console.WriteLine("MyCalculator v1.0 启动中...");
    
    // 加载保存的授权码
    string licenseCode = LoadLicenseFromFile();
    
    if (string.IsNullOrEmpty(licenseCode))
    {
        // 首次运行，请求输入授权码
        Console.Write("请输入授权码: ");
        licenseCode = Console.ReadLine();
    }
    
    // 验证授权
    if (LicenseManager.IsLicenseValid(licenseCode))
    {
        Console.WriteLine("授权验证成功，软件正在启动...");
        SaveLicenseToFile(licenseCode); // 保存授权码
        RunApplication(); // 运行主程序
    }
    else
    {
        Console.WriteLine("授权验证失败，程序无法运行");
        Console.WriteLine("请联系软件提供商获取有效授权码");
    }
}
```

### Python 集成

#### 1. 创建授权管理器
```python
import ctypes

class LicenseManager:
    def __init__(self, software_id):
        self.software_id = software_id  # 设置软件标识
        self.lib = ctypes.CDLL('./LicenseValidator.dll')  # Windows
        # self.lib = ctypes.CDLL('./LicenseValidator.so')   # Linux
        
        # 定义函数签名
        self.lib.ValidateLicenseWithSoftwareId.argtypes = [ctypes.c_char_p, ctypes.c_char_p, ctypes.c_char_p]
        self.lib.ValidateLicenseWithSoftwareId.restype = ctypes.c_int
    
    def is_license_valid(self, license_code):
        hardware_id = self.get_hardware_fingerprint()
        result = self.lib.ValidateLicenseWithSoftwareId(
            license_code.encode('utf-8'),
            hardware_id.encode('utf-8'),
            self.software_id.encode('utf-8')
        )
        return result == 0
```

#### 2. 在程序中使用
```python
def main():
    print("MyDataAnalyzer v2.1 启动中...")
    
    # 创建授权管理器（重要：设置正确的软件标识）
    license_manager = LicenseManager("MyDataAnalyzer_v2.1")
    
    # 加载或请求授权码
    license_code = load_license_from_file()
    if not license_code:
        license_code = input("请输入授权码: ")
    
    # 验证授权
    if license_manager.is_license_valid(license_code):
        print("授权验证成功，软件正在启动...")
        save_license_to_file(license_code)
        run_application()
    else:
        print("授权验证失败，程序无法运行")

if __name__ == "__main__":
    main()
```

### Java 集成

#### 1. 添加JNA依赖
```xml
<!-- pom.xml -->
<dependency>
    <groupId>net.java.dev.jna</groupId>
    <artifactId>jna</artifactId>
    <version>5.13.0</version>
</dependency>
```

#### 2. 创建授权管理器
```java
import com.sun.jna.Library;
import com.sun.jna.Native;

public class LicenseManager {
    public interface LicenseValidator extends Library {
        LicenseValidator INSTANCE = Native.load("LicenseValidator", LicenseValidator.class);
        int ValidateLicenseWithSoftwareId(String licenseCode, String hardwareId, String softwareId);
    }
    
    private String softwareId;
    
    public LicenseManager(String softwareId) {
        this.softwareId = softwareId; // 设置软件标识
    }
    
    public boolean isLicenseValid(String licenseCode) {
        String hardwareId = getHardwareFingerprint();
        int result = LicenseValidator.INSTANCE.ValidateLicenseWithSoftwareId(
            licenseCode, hardwareId, softwareId);
        return result == 0;
    }
}
```

#### 3. 在主程序中使用
```java
public class MyImageEditor {
    public static void main(String[] args) {
        System.out.println("MyImageEditor v3.0 启动中...");
        
        // 创建授权管理器（重要：设置正确的软件标识）
        LicenseManager licenseManager = new LicenseManager("MyImageEditor_v3.0");
        
        // 加载或请求授权码
        String licenseCode = loadLicenseFromFile();
        if (licenseCode == null || licenseCode.isEmpty()) {
            Scanner scanner = new Scanner(System.in);
            System.out.print("请输入授权码: ");
            licenseCode = scanner.nextLine();
        }
        
        // 验证授权
        if (licenseManager.isLicenseValid(licenseCode)) {
            System.out.println("授权验证成功，软件正在启动...");
            saveLicenseToFile(licenseCode);
            runApplication();
        } else {
            System.out.println("授权验证失败，程序无法运行");
        }
    }
}
```

## 📝 实际应用示例

### 示例1：多个桌面工具

假设您开发了三个桌面工具：

```csharp
// 计算器软件
public class Calculator {
    private static string SOFTWARE_ID = "Calculator_v1.0";
    // ... 验证逻辑
}

// 文本编辑器
public class TextEditor {
    private static string SOFTWARE_ID = "TextEditor_v2.0";
    // ... 验证逻辑
}

// 图片查看器
public class ImageViewer {
    private static string SOFTWARE_ID = "ImageViewer_v1.5";
    // ... 验证逻辑
}
```

每个软件都需要独立的授权码：
- Calculator_v1.0 → 授权码A
- TextEditor_v2.0 → 授权码B  
- ImageViewer_v1.5 → 授权码C

### 示例2：企业内部工具

```python
# 数据导入工具
class DataImporter:
    def __init__(self):
        self.license_manager = LicenseManager("DataImporter_v1.0")

# 报表生成工具  
class ReportGenerator:
    def __init__(self):
        self.license_manager = LicenseManager("ReportGenerator_v2.0")

# 数据分析工具
class DataAnalyzer:
    def __init__(self):
        self.license_manager = LicenseManager("DataAnalyzer_v3.0")
```

## ⚠️ 重要注意事项

### 1. 软件标识唯一性
```csharp
// ✅ 正确：每个软件使用不同标识
string calculatorId = "Calculator_v1.0";
string editorId = "TextEditor_v2.0";

// ❌ 错误：多个软件使用相同标识
string commonId = "MyApp_v1.0"; // 这样会导致授权码通用
```

### 2. 版本管理
```csharp
// 软件升级时建议更新标识
string oldVersion = "MyApp_v1.0";
string newVersion = "MyApp_v2.0"; // 新版本需要新的授权码
```

### 3. 授权码管理
- 为每个软件生成专门的授权码
- 在授权码文件中标明对应的软件
- 建立授权码与软件的对应关系记录

## 🔍 故障排除

### 问题1：授权验证失败
**可能原因：**
- 软件标识不匹配
- 授权码是为其他软件生成的
- 硬件指纹变化

**解决方法：**
```csharp
// 检查软件标识是否正确
Console.WriteLine($"当前软件标识: {SOFTWARE_ID}");

// 检查授权码信息
string decoded = DecodeBase64(licenseCode);
Console.WriteLine($"授权码信息: {decoded}");
```

### 问题2：无法加载验证库
**解决方法：**
- 确保 `LicenseValidator.dll` 在程序目录
- 检查系统架构匹配（x64）
- 安装必要的运行时库

## 📞 技术支持

如需帮助，请参考：
- `LICENSE_SYSTEM_DOCUMENTATION.md` - 完整技术文档
- `Examples/` - 各语言完整示例
- `QUICK_START.md` - 快速开始指南

---

**🎉 恭喜！您已经掌握了独立授权的集成方法！**

现在您可以为每个软件设置专门的授权，实现精确的授权管理。
