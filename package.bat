@echo off
echo ========================================
echo 授权系统打包脚本 - 生成独立exe文件
echo ========================================
echo.

:: 检查必要工具
where dotnet >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 .NET SDK，请先安装 .NET 6.0 或更高版本
    pause
    exit /b 1
)

where cmake >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 CMake，请先安装 CMake
    pause
    exit /b 1
)

:: 创建打包目录
if exist "package" rmdir /s /q "package"
mkdir package
mkdir package\Portable
mkdir package\Standalone

echo 1. 构建 C++ 验证库...
cd LicenseValidator
if not exist "build" mkdir build
cd build

:: 配置 CMake
cmake .. -G "Visual Studio 17 2022" -A x64
if %errorlevel% neq 0 (
    echo 错误: CMake 配置失败
    cd ..\..
    pause
    exit /b 1
)

:: 构建项目
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo 错误: C++ 库构建失败
    cd ..\..
    pause
    exit /b 1
)

:: 复制验证库
copy "lib\Release\LicenseValidator.dll" "..\..\package\Portable\" >nul
copy "lib\Release\LicenseValidator.dll" "..\..\package\Standalone\" >nul
cd ..\..

echo ✓ C++ 验证库构建完成

echo.
echo 2. 打包授权码生成器（独立exe）...
cd LicenseGenerator

:: 发布为单文件exe
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true
if %errorlevel% neq 0 (
    echo 错误: 授权码生成器打包失败
    cd ..
    pause
    exit /b 1
)

:: 复制生成的独立exe
copy "bin\Release\net6.0-windows\win-x64\publish\LicenseGenerator.exe" "..\package\Standalone\" >nul
cd ..

echo ✓ 授权码生成器打包完成

echo.
echo 3. 打包紧急生成器（独立exe）...
cd EmergencyLicenseGenerator

:: 发布为单文件exe
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true
if %errorlevel% neq 0 (
    echo 错误: 紧急生成器打包失败
    cd ..
    pause
    exit /b 1
)

:: 复制生成的独立exe
copy "bin\Release\net6.0\win-x64\publish\EmergencyLicenseGenerator.exe" "..\package\Standalone\" >nul
cd ..

echo ✓ 紧急生成器打包完成

echo.
echo 4. 打包示例程序（独立exe）...
cd Examples\CSharp-Example

:: 发布为单文件exe
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true
if %errorlevel% neq 0 (
    echo 错误: 示例程序打包失败
    cd ..\..
    pause
    exit /b 1
)

:: 复制生成的独立exe
copy "bin\Release\net6.0\win-x64\publish\LicenseExample.exe" "..\..\package\Standalone\" >nul
cd ..\..

echo ✓ 示例程序打包完成

echo.
echo 5. 创建便携式版本...
:: 复制便携式版本文件
copy "bin\LicenseGenerator.exe" "package\Portable\" >nul 2>nul
copy "bin\EmergencyLicenseGenerator.exe" "package\Portable\" >nul 2>nul
copy "bin\LicenseExample.exe" "package\Portable\" >nul 2>nul
copy "bin\*.dll" "package\Portable\" >nul 2>nul

echo ✓ 便携式版本创建完成

echo.
echo 6. 复制文档和示例...
:: 复制文档
copy "LICENSE_SYSTEM_DOCUMENTATION.md" "package\Portable\" >nul
copy "LICENSE_SYSTEM_DOCUMENTATION.md" "package\Standalone\" >nul
copy "README.md" "package\Portable\" >nul
copy "README.md" "package\Standalone\" >nul
copy "QUICK_START.md" "package\Portable\" >nul
copy "QUICK_START.md" "package\Standalone\" >nul
copy "INTEGRATION_TUTORIAL.md" "package\Portable\" >nul
copy "INTEGRATION_TUTORIAL.md" "package\Standalone\" >nul
copy "INDEPENDENT_LICENSE_SUMMARY.md" "package\Portable\" >nul
copy "INDEPENDENT_LICENSE_SUMMARY.md" "package\Standalone\" >nul

:: 复制示例代码
xcopy "Examples" "package\Portable\Examples\" /E /I /Q >nul
xcopy "Examples" "package\Standalone\Examples\" /E /I /Q >nul

echo ✓ 文档和示例复制完成

echo.
echo 7. 创建启动脚本...
:: 创建便携式版本启动脚本
echo @echo off > "package\Portable\启动授权码生成器.bat"
echo echo 启动授权码生成器... >> "package\Portable\启动授权码生成器.bat"
echo LicenseGenerator.exe >> "package\Portable\启动授权码生成器.bat"

echo @echo off > "package\Portable\启动紧急生成器.bat"
echo echo 启动紧急生成器... >> "package\Portable\启动紧急生成器.bat"
echo EmergencyLicenseGenerator.exe >> "package\Portable\启动紧急生成器.bat"

echo @echo off > "package\Portable\测试示例程序.bat"
echo echo 测试示例程序... >> "package\Portable\测试示例程序.bat"
echo LicenseExample.exe >> "package\Portable\测试示例程序.bat"

:: 创建独立版本启动脚本
echo @echo off > "package\Standalone\启动授权码生成器.bat"
echo echo 启动授权码生成器... >> "package\Standalone\启动授权码生成器.bat"
echo LicenseGenerator.exe >> "package\Standalone\启动授权码生成器.bat"

echo @echo off > "package\Standalone\启动紧急生成器.bat"
echo echo 启动紧急生成器... >> "package\Standalone\启动紧急生成器.bat"
echo EmergencyLicenseGenerator.exe >> "package\Standalone\启动紧急生成器.bat"

echo @echo off > "package\Standalone\测试示例程序.bat"
echo echo 测试示例程序... >> "package\Standalone\测试示例程序.bat"
echo LicenseExample.exe >> "package\Standalone\测试示例程序.bat"

echo ✓ 启动脚本创建完成

echo.
echo 8. 创建说明文件...
echo 授权系统 - 便携式版本 > "package\Portable\使用说明.txt"
echo ======================== >> "package\Portable\使用说明.txt"
echo. >> "package\Portable\使用说明.txt"
echo 本版本需要安装 .NET 6.0 Runtime 才能运行 >> "package\Portable\使用说明.txt"
echo. >> "package\Portable\使用说明.txt"
echo 文件说明: >> "package\Portable\使用说明.txt"
echo - LicenseGenerator.exe: 授权码生成器（图形界面） >> "package\Portable\使用说明.txt"
echo - EmergencyLicenseGenerator.exe: 紧急生成器（命令行） >> "package\Portable\使用说明.txt"
echo - LicenseValidator.dll: 验证库（集成用） >> "package\Portable\使用说明.txt"
echo - LicenseExample.exe: 集成示例程序 >> "package\Portable\使用说明.txt"
echo. >> "package\Portable\使用说明.txt"
echo 快速开始: >> "package\Portable\使用说明.txt"
echo 1. 双击"启动授权码生成器.bat"开始使用 >> "package\Portable\使用说明.txt"
echo 2. 阅读 QUICK_START.md 了解详细使用方法 >> "package\Portable\使用说明.txt"

echo 授权系统 - 独立版本 > "package\Standalone\使用说明.txt"
echo ==================== >> "package\Standalone\使用说明.txt"
echo. >> "package\Standalone\使用说明.txt"
echo 本版本为独立exe文件，无需安装任何运行时环境 >> "package\Standalone\使用说明.txt"
echo 可以在任何Windows电脑上直接运行 >> "package\Standalone\使用说明.txt"
echo. >> "package\Standalone\使用说明.txt"
echo 文件说明: >> "package\Standalone\使用说明.txt"
echo - LicenseGenerator.exe: 授权码生成器（图形界面，约50MB） >> "package\Standalone\使用说明.txt"
echo - EmergencyLicenseGenerator.exe: 紧急生成器（命令行，约30MB） >> "package\Standalone\使用说明.txt"
echo - LicenseValidator.dll: 验证库（集成用） >> "package\Standalone\使用说明.txt"
echo - LicenseExample.exe: 集成示例程序（约30MB） >> "package\Standalone\使用说明.txt"
echo. >> "package\Standalone\使用说明.txt"
echo 快速开始: >> "package\Standalone\使用说明.txt"
echo 1. 双击"启动授权码生成器.bat"开始使用 >> "package\Standalone\使用说明.txt"
echo 2. 阅读 QUICK_START.md 了解详细使用方法 >> "package\Standalone\使用说明.txt"

echo ✓ 说明文件创建完成

echo.
echo ========================================
echo 打包完成！
echo ========================================
echo.
echo 生成的包位于 package\ 目录：
echo.
echo 📁 package\Portable\     - 便携式版本（需要.NET Runtime）
echo    ├── LicenseGenerator.exe          (约5MB)
echo    ├── EmergencyLicenseGenerator.exe (约1MB)
echo    ├── LicenseValidator.dll          (验证库)
echo    ├── LicenseExample.exe            (示例程序)
echo    └── 完整文档和示例代码
echo.
echo 📁 package\Standalone\   - 独立版本（无需安装运行时）
echo    ├── LicenseGenerator.exe          (约50MB，独立运行)
echo    ├── EmergencyLicenseGenerator.exe (约30MB，独立运行)
echo    ├── LicenseValidator.dll          (验证库)
echo    ├── LicenseExample.exe            (约30MB，独立运行)
echo    └── 完整文档和示例代码
echo.
echo 推荐使用：
echo - 开发环境：使用 Portable 版本（文件小，启动快）
echo - 分发给客户：使用 Standalone 版本（无需安装运行时）
echo.
pause
