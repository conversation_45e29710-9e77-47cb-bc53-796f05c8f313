package com.example.license;

import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.Platform;
import com.sun.jna.Pointer;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.util.Base64;
import java.util.Scanner;

/**
 * 授权管理器 - Java集成示例
 * 演示如何在Java应用中集成授权验证
 */
public class LicenseManager {
    
    // JNA接口定义
    public interface LicenseValidator extends Library {
        // 根据平台选择库文件
        String LIBRARY_NAME = Platform.isWindows() ? "LicenseValidator" : 
                             Platform.isMac() ? "LicenseValidator" : "LicenseValidator";
        
        LicenseValidator INSTANCE = Native.load(LIBRARY_NAME, LicenseValidator.class);
        
        // 导出函数声明
        int ValidateLicense(String licenseCode, String hardwareId);
        int GetRemainingDays(String licenseCode);
        Pointer GetLastError();
        Pointer GetHardwareFingerprint();
        int CheckTimeIntegrity();
        void Cleanup();
    }
    
    // 返回码常量
    public static final int LICENSE_VALID = 0;
    public static final int LICENSE_INVALID = 1;
    public static final int LICENSE_EXPIRED = 2;
    public static final int LICENSE_HARDWARE_MISMATCH = 3;
    public static final int LICENSE_TIME_TAMPERED = 4;
    public static final int LICENSE_FORMAT_ERROR = 5;
    
    private static final String LICENSE_FILE = "license.dat";
    private LicenseValidator validator;
    
    public LicenseManager() {
        try {
            this.validator = LicenseValidator.INSTANCE;
        } catch (UnsatisfiedLinkError e) {
            System.err.println("警告: 无法加载验证库，将使用备用验证方法");
            System.err.println("错误详情: " + e.getMessage());
            this.validator = null;
        }
    }
    
    /**
     * 验证授权码
     * @param licenseCode 授权码
     * @return 验证结果
     */
    public boolean isLicenseValid(String licenseCode) {
        try {
            if (validator == null) {
                return fallbackValidation(licenseCode);
            }
            
            String hardwareId = getHardwareFingerprint();
            int result = validator.ValidateLicense(licenseCode, hardwareId);
            
            switch (result) {
                case LICENSE_VALID:
                    return true;
                case LICENSE_EXPIRED:
                    System.out.println("授权已过期");
                    break;
                case LICENSE_HARDWARE_MISMATCH:
                    System.out.println("硬件指纹不匹配");
                    break;
                case LICENSE_TIME_TAMPERED:
                    System.out.println("检测到时间篡改");
                    break;
                case LICENSE_FORMAT_ERROR:
                    System.out.println("授权码格式错误");
                    break;
                default:
                    System.out.println("授权验证失败");
                    break;
            }
            
            return false;
            
        } catch (Exception e) {
            System.err.println("授权验证异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取剩余天数
     * @param licenseCode 授权码
     * @return 剩余天数，-1表示错误
     */
    public int getRemainingDays(String licenseCode) {
        try {
            if (validator == null) {
                return fallbackGetRemainingDays(licenseCode);
            }
            
            return validator.GetRemainingDays(licenseCode);
        } catch (Exception e) {
            return -1;
        }
    }
    
    /**
     * 获取硬件指纹
     * @return 硬件指纹
     */
    public String getHardwareFingerprint() {
        try {
            if (validator != null) {
                Pointer ptr = validator.GetHardwareFingerprint();
                if (ptr != null) {
                    return ptr.getString(0);
                }
            }
        } catch (Exception e) {
            // 忽略异常，使用备用方案
        }
        
        // 备用方案：自己生成
        return generateHardwareFingerprint();
    }
    
    /**
     * 生成硬件指纹（备用方案）
     */
    private String generateHardwareFingerprint() {
        try {
            String cpuId = getCpuId();
            String motherboardId = getMotherboardId();
            String macAddress = getMacAddress();
            
            String combined = cpuId + "|" + motherboardId + "|" + macAddress;
            
            // 生成SHA256哈希
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(combined.getBytes("UTF-8"));
            
            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return "HW_" + hexString.toString().substring(0, 16).toUpperCase();
            
        } catch (Exception e) {
            System.err.println("生成硬件指纹失败: " + e.getMessage());
            return "HW_FALLBACK_" + System.getProperty("user.name");
        }
    }
    
    private String getCpuId() {
        try {
            if (Platform.isWindows()) {
                Process process = Runtime.getRuntime().exec("wmic cpu get ProcessorId /value");
                try (Scanner scanner = new Scanner(process.getInputStream())) {
                    while (scanner.hasNextLine()) {
                        String line = scanner.nextLine();
                        if (line.startsWith("ProcessorId=")) {
                            return line.substring("ProcessorId=".length()).trim();
                        }
                    }
                }
            } else if (Platform.isLinux()) {
                try (Scanner scanner = new Scanner(new File("/proc/cpuinfo"))) {
                    while (scanner.hasNextLine()) {
                        String line = scanner.nextLine();
                        if (line.startsWith("processor")) {
                            return line.split(":")[1].trim();
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "CPU_UNKNOWN";
    }
    
    private String getMotherboardId() {
        try {
            if (Platform.isWindows()) {
                Process process = Runtime.getRuntime().exec("wmic baseboard get SerialNumber /value");
                try (Scanner scanner = new Scanner(process.getInputStream())) {
                    while (scanner.hasNextLine()) {
                        String line = scanner.nextLine();
                        if (line.startsWith("SerialNumber=")) {
                            return line.substring("SerialNumber=".length()).trim();
                        }
                    }
                }
            } else if (Platform.isLinux()) {
                try {
                    return new String(Files.readAllBytes(Paths.get("/sys/class/dmi/id/board_serial"))).trim();
                } catch (Exception e) {
                    // 忽略异常
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "MB_UNKNOWN";
    }
    
    private String getMacAddress() {
        try {
            java.net.NetworkInterface networkInterface = java.net.NetworkInterface.getNetworkInterfaces()
                    .nextElement();
            byte[] mac = networkInterface.getHardwareAddress();
            if (mac != null) {
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < mac.length; i++) {
                    sb.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? ":" : ""));
                }
                return sb.toString();
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "MAC_UNKNOWN";
    }
    
    /**
     * 备用验证方法（当DLL不可用时）
     */
    private boolean fallbackValidation(String licenseCode) {
        try {
            byte[] decoded = Base64.getDecoder().decode(licenseCode);
            String decodedStr = new String(decoded, "UTF-8");
            String[] parts = decodedStr.split("\\|");
            
            if (parts.length != 3) {
                return false;
            }
            
            long startTimestamp = Long.parseLong(parts[0]);
            int validDays = Integer.parseInt(parts[1]);
            String hardwareId = parts[2];
            
            // 检查硬件指纹
            if (!hardwareId.equals(getHardwareFingerprint())) {
                System.out.println("硬件指纹不匹配");
                return false;
            }
            
            // 检查时间
            long currentTimestamp = System.currentTimeMillis() / 1000;
            long expiryTimestamp = startTimestamp + (validDays * 86400L);
            
            if (currentTimestamp > expiryTimestamp) {
                System.out.println("授权已过期");
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            System.err.println("备用验证失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 备用获取剩余天数方法
     */
    private int fallbackGetRemainingDays(String licenseCode) {
        try {
            byte[] decoded = Base64.getDecoder().decode(licenseCode);
            String decodedStr = new String(decoded, "UTF-8");
            String[] parts = decodedStr.split("\\|");
            
            if (parts.length != 3) {
                return -1;
            }
            
            long startTimestamp = Long.parseLong(parts[0]);
            int validDays = Integer.parseInt(parts[1]);
            
            long currentTimestamp = System.currentTimeMillis() / 1000;
            long expiryTimestamp = startTimestamp + (validDays * 86400L);
            
            if (currentTimestamp > expiryTimestamp) {
                return 0;
            }
            
            return (int) ((expiryTimestamp - currentTimestamp) / 86400);
            
        } catch (Exception e) {
            return -1;
        }
    }
    
    /**
     * 保存授权码到本地
     */
    public void saveLicense(String licenseCode) {
        try {
            // 简单的Base64编码存储
            String encoded = Base64.getEncoder().encodeToString(licenseCode.getBytes("UTF-8"));
            try (FileWriter writer = new FileWriter(LICENSE_FILE)) {
                writer.write(encoded);
            }
        } catch (Exception e) {
            System.err.println("保存授权码失败: " + e.getMessage());
        }
    }
    
    /**
     * 从本地加载授权码
     */
    public String loadLicense() {
        try {
            File file = new File(LICENSE_FILE);
            if (!file.exists()) {
                return null;
            }
            
            try (Scanner scanner = new Scanner(file)) {
                if (scanner.hasNextLine()) {
                    String encoded = scanner.nextLine().trim();
                    return new String(Base64.getDecoder().decode(encoded), "UTF-8");
                }
            }
        } catch (Exception e) {
            System.err.println("加载授权码失败: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        try {
            if (validator != null) {
                validator.Cleanup();
            }
        } catch (Exception e) {
            // 忽略异常
        }
    }
}
