using System;
using System.IO;
using System.Text;
using System.Security.Cryptography;
using Newtonsoft.Json;

namespace EmergencyLicenseGenerator
{
    /// <summary>
    /// 紧急授权码生成器 - 控制台版本
    /// 当图形界面版本不可用时的备用方案
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 紧急授权码生成器 ===");
            Console.WriteLine("Emergency License Generator v1.0");
            Console.WriteLine();

            try
            {
                if (args.Length == 0)
                {
                    ShowUsage();
                    InteractiveMode();
                }
                else if (args.Length == 4)
                {
                    CommandLineMode(args);
                }
                else
                {
                    ShowUsage();
                    Environment.Exit(1);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
                Environment.Exit(1);
            }
        }

        static void ShowUsage()
        {
            Console.WriteLine("用法:");
            Console.WriteLine("  EmergencyLicenseGenerator.exe                                      # 交互模式");
            Console.WriteLine("  EmergencyLicenseGenerator.exe <软件标识> <硬件指纹> <开始日期> <有效天数>  # 命令行模式");
            Console.WriteLine();
            Console.WriteLine("示例:");
            Console.WriteLine("  EmergencyLicenseGenerator.exe MyApp_v1.0 HW_ABC123 2024-01-01 30");
            Console.WriteLine("  EmergencyLicenseGenerator.exe Calculator_v2.1 HW_DEF456 2024-02-15 90");
            Console.WriteLine();
        }

        static void InteractiveMode()
        {
            Console.WriteLine("=== 交互模式 ===");
            Console.WriteLine();

            // 获取软件标识
            Console.Write("请输入软件标识 [默认MyApp_v1.0]: ");
            string? softwareId = Console.ReadLine()?.Trim();
            if (string.IsNullOrEmpty(softwareId))
            {
                softwareId = "MyApp_v1.0";
            }

            // 获取硬件指纹
            Console.Write("请输入硬件指纹: ");
            string? hardwareId = Console.ReadLine()?.Trim();
            if (string.IsNullOrEmpty(hardwareId))
            {
                Console.WriteLine("错误: 硬件指纹不能为空");
                return;
            }

            // 获取开始日期
            Console.Write("请输入开始日期 (yyyy-MM-dd) [默认今天]: ");
            string? startDateInput = Console.ReadLine()?.Trim();
            DateTime startDate;
            if (string.IsNullOrEmpty(startDateInput))
            {
                startDate = DateTime.Today;
            }
            else if (!DateTime.TryParse(startDateInput, out startDate))
            {
                Console.WriteLine("错误: 日期格式无效");
                return;
            }

            // 获取有效天数
            Console.Write("请输入有效天数 [默认30]: ");
            string? validDaysInput = Console.ReadLine()?.Trim();
            int validDays;
            if (string.IsNullOrEmpty(validDaysInput))
            {
                validDays = 30;
            }
            else if (!int.TryParse(validDaysInput, out validDays) || validDays <= 0)
            {
                Console.WriteLine("错误: 有效天数必须是正整数");
                return;
            }

            // 生成授权码
            GenerateAndDisplayLicense(softwareId, hardwareId, startDate, validDays);
        }

        static void CommandLineMode(string[] args)
        {
            string softwareId = args[0];
            string hardwareId = args[1];
            DateTime startDate = DateTime.Parse(args[2]);
            int validDays = int.Parse(args[3]);

            GenerateAndDisplayLicense(softwareId, hardwareId, startDate, validDays);
        }

        static void GenerateAndDisplayLicense(string softwareId, string hardwareId, DateTime startDate, int validDays)
        {
            Console.WriteLine();
            Console.WriteLine("=== 生成授权码 ===");
            Console.WriteLine($"软件标识: {softwareId}");
            Console.WriteLine($"硬件指纹: {hardwareId}");
            Console.WriteLine($"开始日期: {startDate:yyyy-MM-dd}");
            Console.WriteLine($"有效天数: {validDays}");
            Console.WriteLine($"到期日期: {startDate.AddDays(validDays):yyyy-MM-dd}");
            Console.WriteLine();

            try
            {
                string licenseCode = GenerateLicense(startDate, validDays, hardwareId, softwareId);

                Console.WriteLine("生成的授权码:");
                Console.WriteLine(new string('=', 60));
                Console.WriteLine(licenseCode);
                Console.WriteLine(new string('=', 60));
                Console.WriteLine();

                // 保存到文件
                string fileName = $"License_{softwareId}_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                SaveLicenseToFile(fileName, softwareId, hardwareId, startDate, validDays, licenseCode);

                Console.WriteLine($"✓ 授权码已保存到: {fileName}");
                Console.WriteLine();
                Console.WriteLine("请将授权码提供给客户，并妥善保管生成的文件。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成授权码失败: {ex.Message}");
                throw;
            }
        }

        static string GenerateLicense(DateTime startDate, int validDays, string hardwareId, string softwareId)
        {
            // 创建授权信息（新版本）
            var licenseInfo = new
            {
                StartDate = ((DateTimeOffset)startDate).ToUnixTimeSeconds(),
                ValidDays = validDays,
                HardwareId = hardwareId,
                SoftwareId = softwareId,
                Version = "2.0",
                GeneratedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                Generator = "Emergency"
            };

            // 新格式：START_TIMESTAMP|VALID_DAYS|HARDWARE_ID|SOFTWARE_ID|VERSION
            string newFormat = $"{licenseInfo.StartDate}|{validDays}|{hardwareId}|{softwareId}|{licenseInfo.Version}";
            byte[] data = Encoding.UTF8.GetBytes(newFormat);

            return Convert.ToBase64String(data);
        }

        static void SaveLicenseToFile(string fileName, string softwareId, string hardwareId, DateTime startDate, int validDays, string licenseCode)
        {
            var content = new StringBuilder();
            content.AppendLine("紧急授权码生成记录");
            content.AppendLine("Emergency License Generation Record");
            content.AppendLine(new string('=', 60));
            content.AppendLine($"生成时间 (Generated): {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            content.AppendLine($"生成器版本 (Generator): Emergency v2.0");
            content.AppendLine();
            content.AppendLine("授权信息 (License Information):");
            content.AppendLine($"  软件标识 (Software ID): {softwareId}");
            content.AppendLine($"  硬件指纹 (Hardware ID): {hardwareId}");
            content.AppendLine($"  开始日期 (Start Date): {startDate:yyyy-MM-dd}");
            content.AppendLine($"  有效天数 (Valid Days): {validDays}");
            content.AppendLine($"  到期日期 (Expiry Date): {startDate.AddDays(validDays):yyyy-MM-dd}");
            content.AppendLine();
            content.AppendLine("授权码 (License Code):");
            content.AppendLine(new string('-', 60));
            content.AppendLine(licenseCode);
            content.AppendLine(new string('-', 60));
            content.AppendLine();
            content.AppendLine("重要提醒 (Important Notes):");
            content.AppendLine("1. 请妥善保管此授权码，遗失后需要重新生成");
            content.AppendLine("2. 授权码与硬件指纹绑定，不能在其他机器使用");
            content.AppendLine("3. 请确认客户的硬件指纹正确无误");
            content.AppendLine("4. 此文件包含敏感信息，请安全存储");
            content.AppendLine();
            content.AppendLine("1. Keep this license code safe, regeneration required if lost");
            content.AppendLine("2. License code is bound to hardware fingerprint");
            content.AppendLine("3. Please verify customer's hardware fingerprint is correct");
            content.AppendLine("4. This file contains sensitive information, store securely");

            File.WriteAllText(fileName, content.ToString(), Encoding.UTF8);
        }
    }
}
