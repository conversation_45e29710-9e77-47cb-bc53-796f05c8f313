cmake_minimum_required(VERSION 3.16)
project(LicenseValidator)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 源文件
set(SOURCES
    LicenseValidator.cpp
    LicenseValidator.h
)

# 创建动态库
add_library(LicenseValidator SHARED ${SOURCES})

# 设置编译定义
target_compile_definitions(LicenseValidator PRIVATE LICENSEVALIDATOR_EXPORTS)

# Windows特定设置
if(WIN32)
    target_link_libraries(LicenseValidator iphlpapi)
    set_target_properties(LicenseValidator PROPERTIES
        WINDOWS_EXPORT_ALL_SYMBOLS ON
    )
endif()

# Linux特定设置
if(UNIX AND NOT APPLE)
    target_link_libraries(LicenseValidator pthread)
endif()

# 安装规则
install(TARGETS LicenseValidator
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(FILES LicenseValidator.h
    DESTINATION include
)
