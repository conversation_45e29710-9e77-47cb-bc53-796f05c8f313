<Window x:Class="LicenseGenerator.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="授权码生成器 - License Generator v1.0" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanMinimize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="软件授权码生成器" 
                   FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,20" 
                   Foreground="#2E86AB"/>
        
        <!-- 硬件指纹输入 -->
        <GroupBox Grid.Row="1" Header="客户硬件信息" Margin="0,0,0,15">
            <StackPanel Margin="10">
                <TextBlock Text="硬件指纹 (Hardware Fingerprint):" Margin="0,0,0,5"/>
                <TextBox x:Name="txtHardwareFingerprint" 
                         Height="25" 
                         ToolTip="输入客户端的硬件指纹"/>
                <Button x:Name="btnGetLocalFingerprint" 
                        Content="获取本机指纹" 
                        Width="120" 
                        Height="30" 
                        Margin="0,10,0,0" 
                        HorizontalAlignment="Left"
                        Click="BtnGetLocalFingerprint_Click"/>
            </StackPanel>
        </GroupBox>
        
        <!-- 授权期限设置 -->
        <GroupBox Grid.Row="2" Header="授权期限设置" Margin="0,0,0,15">
            <StackPanel Margin="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="开始日期:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <DatePicker x:Name="dpStartDate" Grid.Column="1" Margin="0,0,10,0"/>
                    <Button x:Name="btnToday" Grid.Column="2" Content="今天" Width="60" Click="BtnToday_Click"/>
                </Grid>
                
                <Grid Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="有效天数:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox x:Name="txtValidDays" Grid.Column="1" Height="25" Text="30" Margin="0,0,10,0"/>
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <Button Content="7天" Width="50" Margin="0,0,5,0" Click="BtnSetDays_Click" Tag="7"/>
                        <Button Content="30天" Width="50" Margin="0,0,5,0" Click="BtnSetDays_Click" Tag="30"/>
                        <Button Content="90天" Width="50" Margin="0,0,5,0" Click="BtnSetDays_Click" Tag="90"/>
                        <Button Content="365天" Width="50" Click="BtnSetDays_Click" Tag="365"/>
                    </StackPanel>
                </Grid>
                
                <TextBlock x:Name="lblExpiryDate" 
                           Margin="0,10,0,0" 
                           Foreground="Gray" 
                           Text="到期日期: 将在设置后显示"/>
            </StackPanel>
        </GroupBox>
        
        <!-- 生成按钮 -->
        <Button Grid.Row="3" 
                x:Name="btnGenerate" 
                Content="生成授权码" 
                Height="40" 
                FontSize="16" 
                FontWeight="Bold" 
                Background="#2E86AB" 
                Foreground="White" 
                Margin="0,0,0,15"
                Click="BtnGenerate_Click"/>
        
        <!-- 结果显示 -->
        <GroupBox Grid.Row="4" Header="生成的授权码" Margin="0,0,0,15">
            <StackPanel Margin="10">
                <TextBox x:Name="txtLicenseCode" 
                         Height="80" 
                         TextWrapping="Wrap" 
                         IsReadOnly="True" 
                         VerticalScrollBarVisibility="Auto"
                         FontFamily="Consolas"
                         Background="#F5F5F5"/>
                <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                    <Button x:Name="btnCopy" 
                            Content="复制授权码" 
                            Width="100" 
                            Height="30" 
                            Margin="0,0,10,0"
                            Click="BtnCopy_Click"/>
                    <Button x:Name="btnSave" 
                            Content="保存到文件" 
                            Width="100" 
                            Height="30"
                            Click="BtnSave_Click"/>
                </StackPanel>
            </StackPanel>
        </GroupBox>
        
        <!-- 日志区域 -->
        <GroupBox Grid.Row="5" Header="操作日志">
            <ScrollViewer Margin="10">
                <TextBlock x:Name="txtLog" 
                           TextWrapping="Wrap" 
                           FontFamily="Consolas" 
                           FontSize="12"
                           Background="#FAFAFA"/>
            </ScrollViewer>
        </GroupBox>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="6" Height="25">
            <StatusBarItem>
                <TextBlock x:Name="lblStatus" Text="就绪"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="版本 1.0 | 开发者: [您的名称]"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
