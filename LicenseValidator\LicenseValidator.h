#pragma once

#ifdef _WIN32
    #ifdef LICENSEVALIDATOR_EXPORTS
        #define LICENSEVALIDATOR_API __declspec(dllexport)
    #else
        #define LICENSEVALIDATOR_API __declspec(dllimport)
    #endif
#else
    #define LICENSEVALIDATOR_API
#endif

#ifdef __cplusplus
extern "C" {
#endif

// 返回码定义
#define LICENSE_VALID           0   // 授权有效
#define LICENSE_INVALID         1   // 授权无效
#define LICENSE_EXPIRED         2   // 授权过期
#define LICENSE_HARDWARE_MISMATCH 3 // 硬件不匹配
#define LICENSE_TIME_TAMPERED   4   // 时间被篡改
#define LICENSE_FORMAT_ERROR    5   // 格式错误

// 主要验证函数（新版本 - 包含软件标识）
LICENSEVALIDATOR_API int ValidateLicenseWithSoftwareId(const char* licenseCode, const char* hardwareId, const char* softwareId);

// 兼容性函数（旧版本 - 不验证软件标识）
LICENSEVALIDATOR_API int ValidateLicense(const char* licenseCode, const char* hardwareId);

// 获取剩余天数
LICENSEVALIDATOR_API int GetRemainingDays(const char* licenseCode);

// 获取最后错误信息
LICENSEVALIDATOR_API const char* GetLastError();

// 获取硬件指纹
LICENSEVALIDATOR_API const char* GetHardwareFingerprint();

// 检查时间是否被篡改
LICENSEVALIDATOR_API int CheckTimeIntegrity();

// 初始化时间锚点
LICENSEVALIDATOR_API int InitializeTimeAnchor();

// 清理资源
LICENSEVALIDATOR_API void Cleanup();

#ifdef __cplusplus
}
#endif
