@echo off
echo ========================================
echo 授权系统构建脚本
echo ========================================
echo.

:: 检查必要工具
where cmake >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 CMake，请先安装 CMake
    pause
    exit /b 1
)

where dotnet >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 .NET SDK，请先安装 .NET 6.0 或更高版本
    pause
    exit /b 1
)

:: 创建构建目录
if not exist "build" mkdir build
if not exist "bin" mkdir bin

echo 1. 构建 C++ 验证库...
cd LicenseValidator
if not exist "build" mkdir build
cd build

:: 配置 CMake
cmake .. -G "Visual Studio 17 2022" -A x64
if %errorlevel% neq 0 (
    echo 错误: CMake 配置失败
    cd ..\..
    pause
    exit /b 1
)

:: 构建项目
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo 错误: C++ 库构建失败
    cd ..\..
    pause
    exit /b 1
)

:: 复制生成的 DLL
copy "lib\Release\LicenseValidator.dll" "..\..\bin\" >nul
cd ..\..

echo ✓ C++ 验证库构建完成

echo.
echo 2. 构建 C# 授权码生成器...
cd LicenseGenerator

dotnet build -c Release
if %errorlevel% neq 0 (
    echo 错误: C# 生成器构建失败
    cd ..
    pause
    exit /b 1
)

:: 复制生成的 EXE
copy "bin\Release\net6.0-windows\LicenseGenerator.exe" "..\bin\" >nul
copy "bin\Release\net6.0-windows\*.dll" "..\bin\" >nul
cd ..

echo ✓ C# 授权码生成器构建完成

echo.
echo 3. 构建紧急授权码生成器...
cd ..\EmergencyLicenseGenerator

dotnet build -c Release
if %errorlevel% neq 0 (
    echo 错误: 紧急生成器构建失败
    cd ..
    pause
    exit /b 1
)

:: 复制生成的 EXE
copy "bin\Release\net6.0\EmergencyLicenseGenerator.exe" "..\bin\" >nul
copy "bin\Release\net6.0\*.dll" "..\bin\" >nul
cd ..

echo ✓ 紧急授权码生成器构建完成

echo.
echo 4. 构建示例程序...
cd Examples\CSharp-Example

dotnet build -c Release
if %errorlevel% neq 0 (
    echo 错误: C# 示例构建失败
    cd ..\..
    pause
    exit /b 1
)

:: 复制示例程序
copy "bin\Release\net6.0\LicenseExample.exe" "..\..\bin\" >nul
cd ..\..

echo ✓ C# 示例程序构建完成

echo.
echo 5. 构建Java示例程序...
cd Examples\Java-Example

:: 检查Maven是否可用
where mvn >nul 2>nul
if %errorlevel% neq 0 (
    echo 警告: 未找到 Maven，跳过Java示例构建
    echo 请手动安装Maven并运行: mvn clean package
    cd ..\..
    goto skip_java
)

mvn clean package -q
if %errorlevel% neq 0 (
    echo 警告: Java示例构建失败，但继续其他构建
    cd ..\..
    goto skip_java
)

:: 复制Java示例程序
copy "target\license-example-1.0.0.jar" "..\..\bin\LicenseExample.jar" >nul
cd ..\..

echo ✓ Java示例程序构建完成

:skip_java
echo.
echo 6. 复制必要文件...
:: 复制验证库到示例目录
copy "bin\LicenseValidator.dll" "Examples\CSharp-Example\" >nul
copy "bin\LicenseValidator.dll" "Examples\Python-Example\" >nul

:: 复制文档
copy "LICENSE_SYSTEM_DOCUMENTATION.md" "bin\" >nul

echo ✓ 文件复制完成

echo.
echo ========================================
echo 构建完成！
echo ========================================
echo.
echo 生成的文件位于 bin\ 目录：
echo - LicenseGenerator.exe         (授权码生成器 - WPF图形界面)
echo - EmergencyLicenseGenerator.exe (紧急授权码生成器 - 控制台)
echo - LicenseValidator.dll         (验证库)
echo - LicenseExample.exe           (C# 集成示例)
echo - LicenseExample.jar           (Java 集成示例 - 需要Java运行时)
echo - LICENSE_SYSTEM_DOCUMENTATION.md (完整文档)
echo.
echo 集成示例位于 Examples\ 目录：
echo - CSharp-Example\         (C# 集成示例)
echo - Python-Example\         (Python 集成示例)
echo - Java-Example\           (Java 集成示例)
echo.
echo 请阅读 LICENSE_SYSTEM_DOCUMENTATION.md 了解详细使用方法
echo.
pause
