#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
授权管理器 - Python集成示例
演示如何在Python应用中集成授权验证
"""

import ctypes
import platform
import hashlib
import os
import json
import base64
from typing import Optional
import uuid
import subprocess

class LicenseManager:
    """授权管理器类"""
    
    # 返回码常量
    LICENSE_VALID = 0
    LICENSE_INVALID = 1
    LICENSE_EXPIRED = 2
    LICENSE_HARDWARE_MISMATCH = 3
    LICENSE_TIME_TAMPERED = 4
    LICENSE_FORMAT_ERROR = 5
    
    def __init__(self):
        """初始化授权管理器"""
        self.lib = None
        self.license_file = "license.dat"
        self._load_library()
    
    def _load_library(self):
        """加载验证库"""
        try:
            if platform.system() == "Windows":
                self.lib = ctypes.CDLL('./LicenseValidator.dll')
            elif platform.system() == "Linux":
                self.lib = ctypes.CDLL('./LicenseValidator.so')
            elif platform.system() == "Darwin":  # macOS
                self.lib = ctypes.CDLL('./LicenseValidator.dylib')
            else:
                raise Exception(f"不支持的操作系统: {platform.system()}")
            
            # 定义函数签名
            self.lib.ValidateLicense.argtypes = [ctypes.c_char_p, ctypes.c_char_p]
            self.lib.ValidateLicense.restype = ctypes.c_int
            
            self.lib.GetRemainingDays.argtypes = [ctypes.c_char_p]
            self.lib.GetRemainingDays.restype = ctypes.c_int
            
            self.lib.GetLastError.restype = ctypes.c_char_p
            self.lib.GetHardwareFingerprint.restype = ctypes.c_char_p
            
            self.lib.CheckTimeIntegrity.restype = ctypes.c_int
            self.lib.Cleanup.restype = None
            
        except Exception as e:
            print(f"加载验证库失败: {e}")
            print("将使用备用验证方法")
            self.lib = None
    
    def is_license_valid(self, license_code: str) -> bool:
        """
        验证授权码
        
        Args:
            license_code: 授权码
            
        Returns:
            bool: 验证结果
        """
        try:
            if self.lib is None:
                return self._fallback_validation(license_code)
            
            hardware_id = self.get_hardware_fingerprint()
            result = self.lib.ValidateLicense(
                license_code.encode('utf-8'),
                hardware_id.encode('utf-8')
            )
            
            if result == self.LICENSE_VALID:
                return True
            elif result == self.LICENSE_EXPIRED:
                print("授权已过期")
            elif result == self.LICENSE_HARDWARE_MISMATCH:
                print("硬件指纹不匹配")
            elif result == self.LICENSE_TIME_TAMPERED:
                print("检测到时间篡改")
            elif result == self.LICENSE_FORMAT_ERROR:
                print("授权码格式错误")
            else:
                print("授权验证失败")
            
            return False
            
        except Exception as e:
            print(f"授权验证异常: {e}")
            return False
    
    def get_remaining_days(self, license_code: str) -> int:
        """
        获取剩余天数
        
        Args:
            license_code: 授权码
            
        Returns:
            int: 剩余天数，-1表示错误
        """
        try:
            if self.lib is None:
                return self._fallback_get_remaining_days(license_code)
            
            return self.lib.GetRemainingDays(license_code.encode('utf-8'))
        except:
            return -1
    
    def get_hardware_fingerprint(self) -> str:
        """
        获取硬件指纹
        
        Returns:
            str: 硬件指纹
        """
        try:
            if self.lib is not None:
                result = self.lib.GetHardwareFingerprint()
                if result:
                    return result.decode('utf-8')
        except:
            pass
        
        # 备用方案：自己生成
        return self._generate_hardware_fingerprint()
    
    def _generate_hardware_fingerprint(self) -> str:
        """生成硬件指纹（备用方案）"""
        try:
            cpu_id = self._get_cpu_id()
            motherboard_id = self._get_motherboard_id()
            mac_address = self._get_mac_address()
            
            combined = f"{cpu_id}|{motherboard_id}|{mac_address}"
            
            # 生成SHA256哈希
            hash_obj = hashlib.sha256(combined.encode('utf-8'))
            return "HW_" + hash_obj.hexdigest()[:16].upper()
            
        except Exception as e:
            print(f"生成硬件指纹失败: {e}")
            return f"HW_FALLBACK_{platform.node()}"
    
    def _get_cpu_id(self) -> str:
        """获取CPU ID"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(
                    ['wmic', 'cpu', 'get', 'ProcessorId', '/value'],
                    capture_output=True, text=True
                )
                for line in result.stdout.split('\n'):
                    if 'ProcessorId=' in line:
                        return line.split('=')[1].strip()
            elif platform.system() == "Linux":
                with open('/proc/cpuinfo', 'r') as f:
                    for line in f:
                        if 'processor' in line:
                            return line.split(':')[1].strip()
        except:
            pass
        return "CPU_UNKNOWN"
    
    def _get_motherboard_id(self) -> str:
        """获取主板ID"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(
                    ['wmic', 'baseboard', 'get', 'SerialNumber', '/value'],
                    capture_output=True, text=True
                )
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line:
                        return line.split('=')[1].strip()
            elif platform.system() == "Linux":
                try:
                    with open('/sys/class/dmi/id/board_serial', 'r') as f:
                        return f.read().strip()
                except:
                    pass
        except:
            pass
        return "MB_UNKNOWN"
    
    def _get_mac_address(self) -> str:
        """获取MAC地址"""
        try:
            mac = uuid.getnode()
            return ':'.join(['{:02x}'.format((mac >> elements) & 0xff) 
                           for elements in range(0, 2*6, 2)][::-1])
        except:
            return "MAC_UNKNOWN"
    
    def _fallback_validation(self, license_code: str) -> bool:
        """备用验证方法（当DLL不可用时）"""
        try:
            # 简化的验证逻辑
            decoded = base64.b64decode(license_code).decode('utf-8')
            parts = decoded.split('|')
            
            if len(parts) != 3:
                return False
            
            start_timestamp, valid_days, hardware_id = parts
            
            # 检查硬件指纹
            if hardware_id != self.get_hardware_fingerprint():
                print("硬件指纹不匹配")
                return False
            
            # 检查时间
            import time
            current_timestamp = int(time.time())
            expiry_timestamp = int(start_timestamp) + (int(valid_days) * 86400)
            
            if current_timestamp > expiry_timestamp:
                print("授权已过期")
                return False
            
            return True
            
        except Exception as e:
            print(f"备用验证失败: {e}")
            return False
    
    def _fallback_get_remaining_days(self, license_code: str) -> int:
        """备用获取剩余天数方法"""
        try:
            decoded = base64.b64decode(license_code).decode('utf-8')
            parts = decoded.split('|')
            
            if len(parts) != 3:
                return -1
            
            start_timestamp, valid_days, _ = parts
            
            import time
            current_timestamp = int(time.time())
            expiry_timestamp = int(start_timestamp) + (int(valid_days) * 86400)
            
            if current_timestamp > expiry_timestamp:
                return 0
            
            return (expiry_timestamp - current_timestamp) // 86400
            
        except:
            return -1
    
    def save_license(self, license_code: str):
        """保存授权码到本地"""
        try:
            # 简单的Base64编码存储（实际应用中应使用更强的加密）
            encoded = base64.b64encode(license_code.encode('utf-8')).decode('utf-8')
            with open(self.license_file, 'w') as f:
                f.write(encoded)
        except Exception as e:
            print(f"保存授权码失败: {e}")
    
    def load_license(self) -> Optional[str]:
        """从本地加载授权码"""
        try:
            if not os.path.exists(self.license_file):
                return None
            
            with open(self.license_file, 'r') as f:
                encoded = f.read().strip()
                return base64.b64decode(encoded).decode('utf-8')
        except Exception as e:
            print(f"加载授权码失败: {e}")
            return None
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.lib is not None:
                self.lib.Cleanup()
        except:
            pass


def main():
    """示例应用程序主函数"""
    print("=== Python 授权验证示例程序 ===")
    print()
    
    license_manager = LicenseManager()
    
    try:
        # 显示硬件指纹
        hardware_fingerprint = license_manager.get_hardware_fingerprint()
        print(f"本机硬件指纹: {hardware_fingerprint}")
        print()
        
        # 尝试加载已保存的授权码
        saved_license = license_manager.load_license()
        if saved_license:
            print("发现已保存的授权码，正在验证...")
            if license_manager.is_license_valid(saved_license):
                remaining_days = license_manager.get_remaining_days(saved_license)
                print(f"✓ 授权验证成功！剩余天数: {remaining_days}")
                run_application()
                return
            else:
                print("✗ 已保存的授权码无效")
        
        # 请求输入授权码
        print("请输入授权码:")
        input_license = input().strip()
        
        if not input_license:
            print("未输入授权码，程序退出")
            return
        
        # 验证授权码
        print("正在验证授权码...")
        if license_manager.is_license_valid(input_license):
            remaining_days = license_manager.get_remaining_days(input_license)
            print(f"✓ 授权验证成功！剩余天数: {remaining_days}")
            
            # 保存授权码
            license_manager.save_license(input_license)
            print("授权码已保存，下次启动将自动验证")
            
            run_application()
        else:
            print("✗ 授权验证失败，程序无法运行")
            print("请联系软件提供商获取有效的授权码")
    
    except Exception as e:
        print(f"程序运行异常: {e}")
    
    finally:
        license_manager.cleanup()
        print("\n按回车键退出...")
        input()


def run_application():
    """运行主要应用程序逻辑"""
    import time
    
    print()
    print("=== 应用程序正在运行 ===")
    print("这里是您的软件主要功能...")
    print()
    
    # 模拟应用程序运行
    for i in range(1, 6):
        print(f"执行任务 {i}/5...")
        time.sleep(1)
    
    print("应用程序运行完成！")


if __name__ == "__main__":
    main()
