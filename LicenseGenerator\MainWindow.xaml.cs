using System;
using System.IO;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Security.Cryptography;
using Newtonsoft.Json;
using Microsoft.Win32;
using System.Management;
using System.Net.NetworkInformation;
using System.Linq;

namespace LicenseGenerator
{
    public partial class MainWindow : Window
    {
        private string privateKeyPath = "private.key";
        private string publicKeyPath = "public.key";

        public MainWindow()
        {
            InitializeComponent();
            InitializeApplication();
        }

        private void InitializeApplication()
        {
            dpStartDate.SelectedDate = DateTime.Today;
            UpdateExpiryDate();
            LogMessage("应用程序已启动");
            
            // 检查密钥文件
            if (!File.Exists(privateKeyPath) || !File.Exists(publicKeyPath))
            {
                GenerateKeyPair();
            }
        }

        private void GenerateKeyPair()
        {
            try
            {
                using (var rsa = RSA.Create(2048))
                {
                    // 导出私钥
                    var privateKey = rsa.ExportRSAPrivateKey();
                    File.WriteAllBytes(privateKeyPath, privateKey);
                    
                    // 导出公钥
                    var publicKey = rsa.ExportRSAPublicKey();
                    File.WriteAllBytes(publicKeyPath, publicKey);
                    
                    LogMessage("RSA密钥对已生成");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"生成密钥对失败: {ex.Message}");
                MessageBox.Show($"生成密钥对失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnGetLocalFingerprint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string fingerprint = GetHardwareFingerprint();
                txtHardwareFingerprint.Text = fingerprint;
                LogMessage($"获取本机硬件指纹: {fingerprint}");
            }
            catch (Exception ex)
            {
                LogMessage($"获取硬件指纹失败: {ex.Message}");
                MessageBox.Show($"获取硬件指纹失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GetHardwareFingerprint()
        {
            try
            {
                string cpuId = GetCpuId();
                string motherboardId = GetMotherboardId();
                string macAddress = GetMacAddress();
                
                string combined = $"{cpuId}|{motherboardId}|{macAddress}";
                
                using (var sha256 = SHA256.Create())
                {
                    byte[] hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(combined));
                    return "HW_" + Convert.ToHexString(hash)[..16];
                }
            }
            catch
            {
                return "HW_FALLBACK_" + Environment.MachineName;
            }
        }

        private string GetCpuId()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        return obj["ProcessorId"]?.ToString() ?? "CPU_UNKNOWN";
                    }
                }
            }
            catch { }
            return "CPU_UNKNOWN";
        }

        private string GetMotherboardId()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        return obj["SerialNumber"]?.ToString() ?? "MB_UNKNOWN";
                    }
                }
            }
            catch { }
            return "MB_UNKNOWN";
        }

        private string GetMacAddress()
        {
            try
            {
                var networkInterface = NetworkInterface.GetAllNetworkInterfaces()
                    .FirstOrDefault(nic => nic.OperationalStatus == OperationalStatus.Up && 
                                          nic.NetworkInterfaceType != NetworkInterfaceType.Loopback);
                
                return networkInterface?.GetPhysicalAddress().ToString() ?? "MAC_UNKNOWN";
            }
            catch
            {
                return "MAC_UNKNOWN";
            }
        }

        private void BtnToday_Click(object sender, RoutedEventArgs e)
        {
            dpStartDate.SelectedDate = DateTime.Today;
            UpdateExpiryDate();
        }

        private void BtnSetDays_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string days)
            {
                txtValidDays.Text = days;
                UpdateExpiryDate();
            }
        }

        private void UpdateExpiryDate()
        {
            if (dpStartDate.SelectedDate.HasValue && int.TryParse(txtValidDays.Text, out int days))
            {
                var expiryDate = dpStartDate.SelectedDate.Value.AddDays(days);
                lblExpiryDate.Text = $"到期日期: {expiryDate:yyyy-MM-dd} ({days} 天后)";
            }
            else
            {
                lblExpiryDate.Text = "到期日期: 请设置有效的开始日期和天数";
            }
        }

        private void BtnGenerate_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 验证输入
                if (string.IsNullOrWhiteSpace(txtHardwareFingerprint.Text))
                {
                    MessageBox.Show("请输入硬件指纹", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!dpStartDate.SelectedDate.HasValue)
                {
                    MessageBox.Show("请选择开始日期", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!int.TryParse(txtValidDays.Text, out int validDays) || validDays <= 0)
                {
                    MessageBox.Show("请输入有效的天数", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 生成授权码
                string licenseCode = GenerateLicenseCode(
                    dpStartDate.SelectedDate.Value,
                    validDays,
                    txtHardwareFingerprint.Text.Trim()
                );

                txtLicenseCode.Text = licenseCode;
                LogMessage($"授权码生成成功 - 硬件指纹: {txtHardwareFingerprint.Text.Trim()}, 有效期: {validDays} 天");
                lblStatus.Text = "授权码生成成功";
            }
            catch (Exception ex)
            {
                LogMessage($"生成授权码失败: {ex.Message}");
                MessageBox.Show($"生成授权码失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GenerateLicenseCode(DateTime startDate, int validDays, string hardwareFingerprint)
        {
            // 创建授权信息
            var licenseInfo = new
            {
                StartDate = ((DateTimeOffset)startDate).ToUnixTimeSeconds(),
                ValidDays = validDays,
                HardwareId = hardwareFingerprint,
                Version = "1.0",
                GeneratedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };

            // 序列化为JSON
            string json = JsonConvert.SerializeObject(licenseInfo);
            byte[] data = Encoding.UTF8.GetBytes(json);

            // 使用RSA私钥签名（简化版本）
            // 在实际实现中，这里应该使用完整的RSA签名
            
            // 为了演示，使用简化的格式
            string simpleFormat = $"{licenseInfo.StartDate}|{validDays}|{hardwareFingerprint}";
            byte[] simpleData = Encoding.UTF8.GetBytes(simpleFormat);
            
            return Convert.ToBase64String(simpleData);
        }

        private void BtnCopy_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtLicenseCode.Text))
            {
                Clipboard.SetText(txtLicenseCode.Text);
                LogMessage("授权码已复制到剪贴板");
                lblStatus.Text = "授权码已复制";
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtLicenseCode.Text))
            {
                MessageBox.Show("没有可保存的授权码", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var saveDialog = new SaveFileDialog
            {
                Filter = "文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*",
                DefaultExt = "txt",
                FileName = $"License_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
            };

            if (saveDialog.ShowDialog() == true)
            {
                try
                {
                    var content = new StringBuilder();
                    content.AppendLine("软件授权码");
                    content.AppendLine("=" + new string('=', 50));
                    content.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                    content.AppendLine($"硬件指纹: {txtHardwareFingerprint.Text}");
                    content.AppendLine($"开始日期: {dpStartDate.SelectedDate:yyyy-MM-dd}");
                    content.AppendLine($"有效天数: {txtValidDays.Text}");
                    content.AppendLine($"到期日期: {dpStartDate.SelectedDate?.AddDays(int.Parse(txtValidDays.Text)):yyyy-MM-dd}");
                    content.AppendLine();
                    content.AppendLine("授权码:");
                    content.AppendLine(txtLicenseCode.Text);
                    content.AppendLine();
                    content.AppendLine("注意: 请妥善保管此授权码，遗失后需要重新生成。");

                    File.WriteAllText(saveDialog.FileName, content.ToString(), Encoding.UTF8);
                    LogMessage($"授权码已保存到: {saveDialog.FileName}");
                    lblStatus.Text = "授权码已保存";
                }
                catch (Exception ex)
                {
                    LogMessage($"保存文件失败: {ex.Message}");
                    MessageBox.Show($"保存文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void LogMessage(string message)
        {
            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            txtLog.Text += $"[{timestamp}] {message}\n";
        }

        private void TxtValidDays_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateExpiryDate();
        }

        private void DpStartDate_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateExpiryDate();
        }
    }
}
